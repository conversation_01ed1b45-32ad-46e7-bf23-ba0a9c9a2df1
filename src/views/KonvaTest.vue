<template>
  <div class="konva-test-container">
    <div class="tools">
      <el-button-group>
        <el-button type="primary" @click="clearAll">清除所有点</el-button>
        <el-button type="primary" @click="toggleClosed">{{ isClosed ? '取消闭合' : '闭合曲线' }}</el-button>
      </el-button-group>
      <div class="tension-control">
        <span>曲线张力: {{ tension.toFixed(1) }}</span>
        <el-slider v-model="tension" :min="0" :max="1" :step="0.1" style="width: 200px; margin-left: 10px;"
          @input="handleTensionChange" />
      </div>
    </div>

    <div class="tip" :class="{ 'tip-closed': isClosed }">
      <template v-if="!isClosed">
        提示: 点击曲线上可以插入控制点，点击空白区域添加控制点，点击第一个控制点可以闭合曲线
      </template>
      <template v-else>
        提示: 曲线已闭合，只能在曲线上添加控制点，点击"取消闭合"可以继续在空白区域添加点
      </template>
    </div>

    <v-stage ref="stageRef" :config="stageConfig" @click="handleStageClick" @mousemove="handleStageMouseMove">
      <v-layer ref="layerRef">
        <!-- 曲线 -->
        <v-shape :config="curveConfig" />

        <!-- 控制点 -->
        <v-circle v-for="(anchor, index) in anchors" :key="index" :config="{
          x: anchor.x,
          y: anchor.y,
          radius: index === 0 && anchors.length > 2 && !isClosed && anchor.isHovered ? 10 : 8,
          fill: index === 0 ? '#ff6b6b' : '#6b9aff',
          stroke: index === 0 && anchors.length > 2 && !isClosed ? '#ff0000' : '#333',
          strokeWidth: anchor.isHovered ? 4 : 2,
          draggable: true
        }" @dragmove="handleAnchorDragMove($event, index)" @mouseover="handleAnchorMouseOver(index)"
          @mouseout="handleAnchorMouseOut(index)" @click="handleAnchorClick(index)" />
      </v-layer>
    </v-stage>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { Stage } from 'konva/lib/Stage'
import type { Layer } from 'konva/lib/Layer'
import type { Shape } from 'konva/lib/Shape'
import type { KonvaEventObject } from 'konva/lib/Node'

// 定义锚点类型
interface Anchor {
  x: number
  y: number
  isHovered: boolean
}

// 舞台配置
const stageConfig = {
  width: 800,
  height: 600
}

// 引用
const stageRef = ref<{ getNode(): Stage } | null>(null)
const layerRef = ref<{ getNode(): Layer } | null>(null)

// 曲线控制点
const anchors = ref<Anchor[]>([])

// 曲线配置
const tension = ref(0.3)
const isClosed = ref(false)

// 计算曲线配置
const curveConfig = computed(() => {
  return {
    stroke: '#2080ff',
    strokeWidth: 3,
    lineCap: 'round',
    lineJoin: 'round',
    sceneFunc: (ctx: any, shape: Shape) => {
      if (anchors.value.length < 2) return

      ctx.beginPath()

      // 移动到第一个点
      const firstPoint = anchors.value[0]
      if (!firstPoint) return

      ctx.moveTo(firstPoint.x, firstPoint.y)

      if (anchors.value.length === 2) {
        // 只有两个点时绘制直线
        const secondPoint = anchors.value[1]
        if (!secondPoint) return

        ctx.lineTo(secondPoint.x, secondPoint.y)
      } else {
        // 使用Cardinal样条曲线算法绘制平滑曲线
        for (let i = 0; i < anchors.value.length - 1; i++) {
          // 获取当前段的控制点
          const p0 = i === 0 ?
            (anchors.value[anchors.value.length - 1] || anchors.value[0]) :
            anchors.value[i - 1]
          const p1 = anchors.value[i]
          const p2 = anchors.value[i + 1]
          const p3 = i + 2 < anchors.value.length ?
            anchors.value[i + 2] :
            (isClosed.value ? anchors.value[(i + 2) % anchors.value.length] : p2)

          // 确保所有点存在
          if (!p0 || !p1 || !p2 || !p3) continue

          // 计算Cardinal样条曲线的控制点
          const { cp1, cp2 } = getCardinalControlPoints(p0, p1, p2, p3, tension.value)

          // 绘制贝塞尔曲线
          ctx.bezierCurveTo(cp1.x, cp1.y, cp2.x, cp2.y, p2.x, p2.y)
        }
      }

      // 如果是闭合曲线，则连接回第一个点
      if (isClosed.value && anchors.value.length > 2) {
        const p0 = anchors.value[anchors.value.length - 2]
        const p1 = anchors.value[anchors.value.length - 1]
        const p2 = anchors.value[0]
        const p3 = anchors.value.length > 1 ? anchors.value[1] : p2

        // 确保所有点存在
        if (p0 && p1 && p2 && p3) {
          // 计算Cardinal样条曲线的控制点
          const { cp1, cp2 } = getCardinalControlPoints(p0, p1, p2, p3, tension.value)

          // 绘制贝塞尔曲线
          ctx.bezierCurveTo(cp1.x, cp1.y, cp2.x, cp2.y, p2.x, p2.y)
        }
      }

      ctx.fillStrokeShape(shape)
    }
  }
})

// 计算两点之间的距离
const getDistance = (p1: { x: number, y: number }, p2: { x: number, y: number }): number => {
  return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2))
}

// 计算点到线段的距离
const getDistanceToLine = (
  point: { x: number, y: number },
  lineStart: { x: number, y: number },
  lineEnd: { x: number, y: number }
): number => {
  const A = point.x - lineStart.x
  const B = point.y - lineStart.y
  const C = lineEnd.x - lineStart.x
  const D = lineEnd.y - lineStart.y

  const dot = A * C + B * D
  const lenSq = C * C + D * D
  let param = -1

  if (lenSq !== 0) param = dot / lenSq

  let xx, yy

  if (param < 0) {
    xx = lineStart.x
    yy = lineStart.y
  } else if (param > 1) {
    xx = lineEnd.x
    yy = lineEnd.y
  } else {
    xx = lineStart.x + param * C
    yy = lineStart.y + param * D
  }

  const dx = point.x - xx
  const dy = point.y - yy

  return Math.sqrt(dx * dx + dy * dy)
}

// 计算贝塞尔曲线上的点
const getBezierPoint = (
  t: number,
  p0: { x: number, y: number },
  p1: { x: number, y: number },
  p2: { x: number, y: number },
  p3: { x: number, y: number }
): { x: number, y: number } => {
  const oneMinusT = 1 - t
  const oneMinusTSquared = oneMinusT * oneMinusT
  const oneMinusTCubed = oneMinusTSquared * oneMinusT
  const tSquared = t * t
  const tCubed = tSquared * t

  const x = oneMinusTCubed * p0.x +
    3 * oneMinusTSquared * t * p1.x +
    3 * oneMinusT * tSquared * p2.x +
    tCubed * p3.x

  const y = oneMinusTCubed * p0.y +
    3 * oneMinusTSquared * t * p1.y +
    3 * oneMinusT * tSquared * p2.y +
    tCubed * p3.y

  return { x, y }
}

// 控制点缓存
const controlPointsCache = new Map<string, { cp1: { x: number, y: number }, cp2: { x: number, y: number } }>()

// 计算Cardinal样条曲线的控制点（带缓存）
const getCardinalControlPoints = (
  p0: { x: number, y: number },
  p1: { x: number, y: number },
  p2: { x: number, y: number },
  p3: { x: number, y: number },
  tension: number
): { cp1: { x: number, y: number }, cp2: { x: number, y: number } } => {
  // 创建缓存键
  const cacheKey = JSON.stringify({
    p0x: p0.x, p0y: p0.y,
    p1x: p1.x, p1y: p1.y,
    p2x: p2.x, p2y: p2.y,
    p3x: p3.x, p3y: p3.y,
    tension
  })

  // 检查缓存
  const cached = controlPointsCache.get(cacheKey)
  if (cached) {
    return cached
  }

  // 计算控制点
  const tensionFactor = 6 * (1 - tension)
  const cp1x = p1.x + (p2.x - p0.x) / tensionFactor
  const cp1y = p1.y + (p2.y - p0.y) / tensionFactor
  const cp2x = p2.x - (p3.x - p1.x) / tensionFactor
  const cp2y = p2.y - (p3.y - p1.y) / tensionFactor

  const result = {
    cp1: { x: cp1x, y: cp1y },
    cp2: { x: cp2x, y: cp2y }
  }

  // 存入缓存
  controlPointsCache.set(cacheKey, result)

  // 限制缓存大小，防止内存泄漏
  if (controlPointsCache.size > 1000) {
    // 删除最早添加的项
    const firstKey = controlPointsCache.keys().next().value
    if (firstKey) {
      controlPointsCache.delete(firstKey)
    }
  }

  return result
}

// 查找最近的曲线点并计算插入点
const findInsertionPoint = (point: { x: number, y: number }): { index: number, point: { x: number, y: number } } | null => {
  if (anchors.value.length < 2) return null

  let minDistance = Infinity
  let insertIndex = -1
  let insertPoint = { x: 0, y: 0 }

  // 遍历所有曲线段
  for (let i = 0; i < (isClosed.value ? anchors.value.length : anchors.value.length - 1); i++) {
    // 获取当前段的控制点
    const p0 = i === 0 ?
      (anchors.value[anchors.value.length - 1] || anchors.value[0]) :
      anchors.value[i - 1]
    const p1 = anchors.value[i]
    const p2 = anchors.value[(i + 1) % anchors.value.length]
    const p3 = i + 2 < anchors.value.length ?
      anchors.value[i + 2] :
      (isClosed.value ? anchors.value[(i + 2) % anchors.value.length] : p2)

    // 确保所有点存在
    if (!p0 || !p1 || !p2 || !p3) continue

    // 如果只有两个点，直接计算点到线段的距离
    if (anchors.value.length === 2) {
      // 计算点到线段的距离
      const A = point.x - p1.x
      const B = point.y - p1.y
      const C = p2.x - p1.x
      const D = p2.y - p1.y

      const dot = A * C + B * D
      const lenSq = C * C + D * D
      let param = -1

      if (lenSq !== 0) param = dot / lenSq

      let xx, yy

      if (param < 0) {
        xx = p1.x
        yy = p1.y
      } else if (param > 1) {
        xx = p2.x
        yy = p2.y
      } else {
        xx = p1.x + param * C
        yy = p1.y + param * D
      }

      const distance = getDistance(point, { x: xx, y: yy })

      if (distance < minDistance) {
        minDistance = distance
        insertIndex = i + 1
        insertPoint = { x: xx, y: yy }
      }
    } else {
      // 采样曲线上的点（根据曲线复杂度动态调整采样点数量）
      // 计算曲线长度的近似值
      const curveLength = Math.sqrt(
        Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2)
      )

      // 计算Cardinal样条曲线的控制点
      const controlPoints = getCardinalControlPoints(p0, p1, p2, p3, tension.value)
      const cp1 = controlPoints.cp1
      const cp2 = controlPoints.cp2

      // 计算控制点到直线的距离作为弯曲度指标
      const curvature1 = Math.abs(getDistanceToLine(cp1, p1, p2))
      const curvature2 = Math.abs(getDistanceToLine(cp2, p1, p2))
      const maxCurvature = Math.max(curvature1, curvature2)

      // 根据曲线长度、弯曲度和张力值动态调整采样点数量
      // 曲线越长、越弯曲或张力越小，采样点越多
      const baseSamples = Math.ceil(curveLength / 20) // 基于长度的基础采样点
      const curvatureFactor = Math.ceil(maxCurvature / 5) // 基于弯曲度的额外采样点
      const tensionFactor = Math.ceil((1 - tension.value) * 5) // 基于张力的额外采样点

      // 确保采样点在合理范围内
      const numSamples = Math.max(15, Math.min(30, baseSamples + curvatureFactor + tensionFactor))
      for (let j = 0; j <= numSamples; j++) {
        const t = j / numSamples
        const curvePoint = getBezierPoint(t, p1, cp1, cp2, p2)

        // 计算鼠标点到曲线点的距离
        const distance = getDistance(point, curvePoint)

        if (distance < minDistance) {
          minDistance = distance
          insertIndex = i + 1
          insertPoint = curvePoint
        }
      }
    }
  }

  // 如果距离小于阈值，则认为点击在曲线上
  // 使用更大的阈值(20像素)使得更容易在曲线上点击
  return minDistance < 20 ? { index: insertIndex, point: insertPoint } : null
}

// 处理舞台点击事件
const handleStageClick = (e: KonvaEventObject<MouseEvent>) => {
  const stage = stageRef.value?.getNode()
  if (!stage) return

  // 获取点击位置
  const pos = stage.getPointerPosition()
  if (!pos) return

  // 检查是否点击在控制点上，如果是则不处理
  const clickedOnAnchor = e.target.className === 'Circle'
  if (clickedOnAnchor) return

  // 检查是否点击在第一个点附近，如果是则闭合曲线
  if (anchors.value.length > 2 && anchors.value[0]) {
    const firstPoint = anchors.value[0]
    const distance = getDistance(pos, { x: firstPoint.x, y: firstPoint.y })

    if (distance < 20) {
      isClosed.value = true
      return
    }
  }

  // 如果曲线已经闭合，只允许在曲线上添加点，不允许在空白区域添加点
  if (isClosed.value) {
    // 查找最近的线段并计算插入点
    const insertionPoint = findInsertionPoint(pos)

    // 如果找到了插入点（点击在曲线附近），则在该位置插入新的控制点
    if (insertionPoint) {
      // 在曲线上添加控制点
      anchors.value.splice(insertionPoint.index, 0, {
        x: insertionPoint.point.x,
        y: insertionPoint.point.y,
        isHovered: false
      })

      // 手动触发图层重绘
      layerRef.value?.getNode().batchDraw()
    }
    // 如果曲线已闭合且点击在空白区域，则不做任何操作
  } else {
    // 曲线未闭合，允许在任何地方添加点

    // 查找最近的线段并计算插入点
    const insertionPoint = findInsertionPoint(pos)

    // 如果找到了插入点（点击在曲线附近），则在该位置插入新的控制点
    if (insertionPoint) {
      // 在曲线上添加控制点
      anchors.value.splice(insertionPoint.index, 0, {
        x: insertionPoint.point.x,
        y: insertionPoint.point.y,
        isHovered: false
      })

      // 手动触发图层重绘
      layerRef.value?.getNode().batchDraw()
    } else {
      // 在空白处添加控制点
      anchors.value.push({
        x: pos.x,
        y: pos.y,
        isHovered: false
      })

      // 手动触发图层重绘
      layerRef.value?.getNode().batchDraw()
    }
  }
}

// 处理锚点拖动
const handleAnchorDragMove = (e: KonvaEventObject<DragEvent>, index: number) => {
  const pos = e.target.position()
  const anchor = anchors.value[index]

  // 更新锚点位置
  if (anchor) {
    anchor.x = pos.x
    anchor.y = pos.y

    // 手动触发图层重绘
    layerRef.value?.getNode().batchDraw()
  }
}

// 处理锚点鼠标悬停
const handleAnchorMouseOver = (index: number) => {
  const anchor = anchors.value[index]
  if (anchor) {
    anchor.isHovered = true

    // 如果是第一个点，并且有足够的点可以闭合曲线，显示特殊的指针
    if (index === 0 && anchors.value.length > 2 && !isClosed.value) {
      document.body.style.cursor = 'cell' // 使用cell指针表示可以闭合
    } else {
      document.body.style.cursor = 'pointer'
    }
  }
}

// 处理锚点鼠标离开
const handleAnchorMouseOut = (index: number) => {
  const anchor = anchors.value[index]
  if (anchor) {
    anchor.isHovered = false
    document.body.style.cursor = 'default'
  }
}

// 处理锚点点击事件
const handleAnchorClick = (index: number) => {
  // 如果点击的是第一个点，并且有足够的点可以闭合曲线
  if (index === 0 && anchors.value.length > 2 && !isClosed.value) {
    isClosed.value = true
    // 手动触发图层重绘
    layerRef.value?.getNode().batchDraw()
  }
}

// 清除所有点
const clearAll = () => {
  // 清空锚点数组
  anchors.value = []
  isClosed.value = false

  // 清空控制点缓存，因为所有点都被删除了
  controlPointsCache.clear()

  // 手动触发图层重绘
  layerRef.value?.getNode().batchDraw()
}

// 切换闭合状态
const toggleClosed = () => {
  if (anchors.value.length > 2) {
    isClosed.value = !isClosed.value
    // 手动触发图层重绘
    layerRef.value?.getNode().batchDraw()
  }
}

// 处理张力变化
const handleTensionChange = () => {
  // 手动触发图层重绘
  layerRef.value?.getNode().batchDraw()
}

// 用于节流的变量
let lastMoveTime = 0
const throttleDelay = 30 // 30ms的节流延迟

// 处理舞台鼠标移动
const handleStageMouseMove = (e: KonvaEventObject<MouseEvent>) => {
  // 使用节流控制频繁调用
  const now = Date.now()
  if (now - lastMoveTime < throttleDelay) return
  lastMoveTime = now

  const stage = stageRef.value?.getNode()
  if (!stage) return

  // 获取鼠标位置
  const pos = stage.getPointerPosition()
  if (!pos) return

  // 检查是否点击在控制点上
  const clickedOnAnchor = e.target.className === 'Circle'
  if (clickedOnAnchor) return

  // 查找最近的线段
  const insertionPoint = findInsertionPoint(pos)

  // 如果鼠标在曲线附近，显示特殊的鼠标指针
  if (insertionPoint) {
    document.body.style.cursor = 'crosshair' // 使用十字光标表示可以添加点
  } else {
    // 如果不在曲线上且不在控制点上，恢复默认指针
    if (document.body.style.cursor !== 'pointer') {
      document.body.style.cursor = 'default'
    }
  }
}

// 清理函数
const cleanupResources = () => {
  // 清空缓存
  controlPointsCache.clear()
  // 重置状态
  document.body.style.cursor = 'default'
}

// 组件挂载后的初始化
onMounted(() => {
  // 初始化示例点
  anchors.value = [
    { x: 100, y: 100, isHovered: false },
    { x: 200, y: 150, isHovered: false },
    { x: 300, y: 100, isHovered: false }
  ]
})

// 组件卸载前的清理
onUnmounted(() => {
  cleanupResources()
})
</script>

<style scoped lang="less">
.konva-test-container {
  padding: 20px;

  .tools {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;

    .tension-control {
      display: flex;
      align-items: center;
      background: #f5f7fa;
      padding: 8px 15px;
      border-radius: 4px;

      span {
        white-space: nowrap;
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .tip {
    margin-bottom: 15px;
    padding: 10px 15px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 4px;
    color: #1890ff;
    font-size: 14px;
    transition: all 0.3s ease;

    &.tip-closed {
      background-color: #fff7e6;
      border-color: #ffd591;
      color: #fa8c16;
    }
  }

  canvas {
    border: 1px solid #ccc;
    background: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
  }
}
</style>
