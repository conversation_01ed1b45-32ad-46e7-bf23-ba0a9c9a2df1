<script setup lang="ts">
import StepBar from '@/components/StepBar.vue'
</script>

<template>
  <div class="main-layout">
    <!-- 顶部步骤栏 -->
    <StepBar />
    <!-- 主内容区，路由切换显示不同页面 -->
    <div class="main-content">
      <router-view v-slot="{ Component, route }">
        <keep-alive
          :include="[
            'ImportImage',
            'FaceLandmark',
            'LipEdit',
            'Align',
            'VeneerAdjust',
            'VisualOptimize',
            'RetouchTools',
            'ResultShare'
          ]"
        >
          <component :is="Component" :key="route.name" />
        </keep-alive>
      </router-view>
    </div>
  </div>
</template>

<style lang="less" scoped>
.main-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f7f7f7;
}
.step-bar {
  height: 80px;
  background: #fff;
  box-shadow: 0 1px 5px #b3b3b3;
  display: flex;
  align-items: center;
}
.main-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  // padding: 40px 0;
}
</style>
