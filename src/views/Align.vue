<template>
  <div class="align-page" :style="{ height: `calc(100vh - ${headerHeight}px)` }">
    <div class="align-main">
      <!-- 左侧：层叠预览 -->
      <div class="align-preview">
        <AlignPreviewBlock
          :smile-image="smileImage"
          :mouth-image="mouthImage"
          :smile-lip-points="smileLipPoints"
          :mouth-lip-points="mouthLipPoints"
          :mouth-to-smile-transform="mouthToSmileTransform"
          :mouth-align-points="mouthAlignPoints"
          :smile-align-points="smileAlignPoints"
          :opacity="0.5"
        />
      </div>
      <!-- 右侧：两张原图及定位点（Konva 渲染） -->
      <div class="align-side">
        <AlignImageBlock
          :img-src="smileImage"
          label="微笑照"
          v-bind="smileBlockProps"
          :is-smile-image="true"
        />
        <AlignImageBlock
          :img-src="mouthImage"
          label="开口照"
          v-bind="mouthBlockProps"
          :is-smile-image="false"
        />
      </div>
    </div>
    <!-- 底部操作按钮 -->
    <div class="align-btns">
      <div class="align-btn" title="手动对齐照片" @click="startManualAlign">
        <img src="@/assets/images/align-manual.png" class="align-btn-icon" />
        <span>手动对齐照片</span>
      </div>
      <div class="align-btn" title="重设对齐状态" @click="resetAlign">
        <el-icon style="margin-left: 8px">
          <RefreshLeft />
        </el-icon>
        <span>重设对齐状态</span>
      </div>
    </div>
    <!-- 点选提示 -->
    <div
      style="
        position: fixed;
        left: 50%;
        top: 110px;
        transform: translateX(-50%);
        font-size: 16px;
        color: #1e90ff;
        z-index: 30;
        background: #fff;
        border-radius: 8px;
        padding: 8px 18px;
        box-shadow: 0 2px 8px #0001;
      "
    >
      {{ selectTips }}
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, computed, ref } from 'vue'
import { useCommonStore } from '@/store/common'
import { storeToRefs } from 'pinia'
import { RefreshLeft } from '@element-plus/icons-vue'
import AlignImageBlock from '@/components/align/AlignImageBlock.vue'
import AlignPreviewBlock from '@/components/align/AlignPreviewBlock.vue'
import { useFaceData } from '@/composables/useFaceData'

// 是否正在手动对齐（移到组件局部状态）
const isManualSelecting = ref(false)

const commonStore = useCommonStore()
const {
  smileImage,
  mouthImage,
  smileLipPoints,
  mouthLipPoints,
  mouthToSmileTransform,
  headerHeight
} = storeToRefs(commonStore)

// 使用面部数据管理
const faceData = useFaceData()
const { smileAlignPoints, mouthAlignPoints, clearAlignPoints, setMouthToSmileTransform } = faceData

// 点选提示文案
const selectTips = computed(() => {
  if (!isManualSelecting.value) {
    return '移动右边的点，使左边图片对齐'
  }
  const smileNeed = 2 - smileAlignPoints.value.length
  const mouthNeed = 2 - mouthAlignPoints.value.length
  let tips = []
  if (smileNeed > 0) tips.push(`请在微笑照上点第${smileAlignPoints.value.length + 1}个点`)
  if (mouthNeed > 0) tips.push(`请在开口照上点第${mouthAlignPoints.value.length + 1}个点`)
  return tips.join('&')
})

// 点击"手动对齐"
function startManualAlign() {
  isManualSelecting.value = true
  clearAlignPoints()
}

// 点选回调
function handleSmileSelect(pt: { x: number; y: number }) {
  if (!isManualSelecting.value) return
  if (smileAlignPoints.value.length < 2) {
    smileAlignPoints.value.push(pt)
    checkManualSelectFinish()
  }
}
function handleMouthSelect(pt: { x: number; y: number }) {
  if (!isManualSelecting.value) return
  if (mouthAlignPoints.value.length < 2) {
    mouthAlignPoints.value.push(pt)
    checkManualSelectFinish()
  }
}

// 计算仿射变换参数并保存
function doCalcMouthToSmileTransformAndSave() {
  const t = calcMouthToSmileTransform()
  if (t) setMouthToSmileTransform(t)
}

function checkManualSelectFinish() {
  if (smileAlignPoints.value.length >= 2 && mouthAlignPoints.value.length >= 2) {
    isManualSelecting.value = false
    doCalcMouthToSmileTransformAndSave()
  }
}

// 重设为AI自动对齐
function resetAlign() {
  isManualSelecting.value = false
  faceData.resetAlignPoints()
  doCalcMouthToSmileTransformAndSave()
}

// 拖动对齐点回调
const handleSmilePointMove = (idx: number, pt: { x: number; y: number }) => {
  smileAlignPoints.value[idx] = pt
  // 拖动时也要重新计算 mouthToSmileTransform
  doCalcMouthToSmileTransformAndSave()
}
const handleMouthPointMove = (idx: number, pt: { x: number; y: number }) => {
  mouthAlignPoints.value[idx] = pt
  // 拖动时也要重新计算 mouthToSmileTransform
  doCalcMouthToSmileTransformAndSave()
}

// 右侧图片点渲染/点选 props
const smileBlockProps = computed(() => ({
  points: smileAlignPoints.value,
  lipPoints: smileLipPoints.value,
  onPointSelect: handleSmileSelect,
  isSelecting: isManualSelecting.value && smileAlignPoints.value.length < 2,
  selectingIndex: smileAlignPoints.value.length,
  onPointMove: handleSmilePointMove
}))
const mouthBlockProps = computed(() => ({
  points: mouthAlignPoints.value,
  lipPoints: mouthLipPoints.value,
  onPointSelect: handleMouthSelect,
  isSelecting: isManualSelecting.value && mouthAlignPoints.value.length < 2,
  selectingIndex: mouthAlignPoints.value.length,
  onPointMove: handleMouthPointMove
}))

onMounted(() => {
  doCalcMouthToSmileTransformAndSave()
})

/**
 * 计算开口照对齐到微笑照的仿射变换参数
 *
 * 目标：
 *   使得 mouthAlignPoints[0] 经过变换后与 smileAlignPoints[0] 重合，
 *   mouthAlignPoints[1] 经过同样的变换后与 smileAlignPoints[1] 重合。
 *
 * 输入：
 *   - smileAlignPoints: 微笑照的对齐点（原图坐标系）
 *   - mouthAlignPoints: 开口照的对齐点（原图坐标系）
 * 输出：
 *   - angle: 旋转角度（弧度）
 *   - scale: 缩放比例
 *   - offsetX, offsetY: 平移量（变换后 mouthAlignPoints[0] 到 smileAlignPoints[0] 的偏移）
 *
 * 算法：
 *   1. 计算两组点的距离比，得到缩放 scale
 *   2. 计算两组点的夹角差，得到旋转 angle
 *   3. 将 mouthAlignPoints[0] 先缩放、再旋转，最后平移到 smileAlignPoints[0]
 *   4. 返回 { angle, scale, offsetX, offsetY }
 */
function calcMouthToSmileTransform() {
  if (smileAlignPoints.value.length < 2 || mouthAlignPoints.value.length < 2) {
    return null
  }
  const S0 = smileAlignPoints.value[0]
  const S1 = smileAlignPoints.value[1]
  const M0 = mouthAlignPoints.value[0]
  const M1 = mouthAlignPoints.value[1]
  if (!S0 || !S1 || !M0 || !M1) return null

  // 1. 缩放
  const dist = (a: any, b: any) => Math.hypot(a.x - b.x, a.y - b.y)
  const scale = dist(S0, S1) / dist(M0, M1)

  // 2. 旋转
  const angle = Math.atan2(S1.y - S0.y, S1.x - S0.x) - Math.atan2(M1.y - M0.y, M1.x - M0.x)

  // 3. 先将 M0 以原点为基准，缩放、旋转
  const cosA = Math.cos(angle)
  const sinA = Math.sin(angle)
  const mx = M0.x * scale
  const my = M0.y * scale
  const mxr = mx * cosA - my * sinA
  const myr = mx * sinA + my * cosA

  // 4. 平移量 = S0 - (M0 经过缩放、旋转后的新位置)
  const offsetX = S0.x - mxr
  const offsetY = S0.y - myr

  return { angle, scale, offsetX, offsetY }
}
</script>
<style scoped>
.align-page {
  display: flex;
  flex-direction: column;
  background: #f7f8fa;
}
.align-main {
  display: flex;
  flex: 1;
  width: 100vw;
  height: 100%;
  margin: 0 auto;
  /* padding: 32px 0 80px 0; */
  justify-content: center;
  align-items: flex-start;
  box-sizing: border-box;
}
.align-preview {
  flex: 2;
  height: 100%;
  min-width: 600px;
  height: 100%;
  background: #fff;
  box-shadow: 0 2px 16px #0001;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.align-side {
  min-width: 300px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 100%;
  overflow: hidden;
}

.align-btns {
  position: fixed;
  left: 50%;
  bottom: 32px;
  transform: translateX(-50%);
  display: flex;
  gap: 32px;
  z-index: 20;
}
.align-btn {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px #0002;
  padding: 8px 18px;
  font-size: 18px;
  color: #222;
  cursor: pointer;
  user-select: none;
  transition: box-shadow 0.2s;
  &:hover {
    box-shadow: 0 4px 10px #0003;
  }
  &:active {
    box-shadow: 0 2px 4px #0003;
    opacity: 0.8;
  }
}
.align-btn-icon {
  width: 38px;
  height: 38px;
  margin-right: 10px;
}
</style>
