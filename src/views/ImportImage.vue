<template>
  <div class="import-image-container">
    <div class="import-header">
      <h1 class="import-title">导入或选择照片</h1>
      <p class="import-desc">清晰的微笑照片有助于产生更好的结果</p>
    </div>

    <div class="upload-area">
      <!-- 微笑照片上传 -->
      <div class="upload-box">
        <div class="upload-content">
          <ImageUploader
            v-model="smileImage"
            placeholder=""
            :icon-src="smileIconSrc"
            @upload-success="handleSmileUploadSuccess"
            @upload-error="handleUploadError"
          />
        </div>
        <div class="upload-label">点击或拖拽微笑照片到此处</div>
      </div>

      <!-- 开口器照片上传 -->
      <div class="upload-box">
        <div class="upload-content">
          <ImageUploader
            v-model="mouthImage"
            placeholder=""
            :icon-src="mouthIconSrc"
            @upload-success="handleMouthUploadSuccess"
            @upload-error="handleUploadError"
          />
        </div>
        <div class="upload-label">点击或拖拽开口照片到此处</div>
      </div>
    </div>

    <!-- 提示信息 -->
    <div class="action-tip" v-if="canProceed">
      <p>照片已上传，请点击顶部步骤栏的"定位关键点"继续</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useStepStore } from '@/store/step'
import { useImageUpload } from '@/composables/useImageUpload'
import ImageUploader from '@/components/ImageUploader.vue'

// 引入图片资源
import smileIcon from '@/assets/images/smile.png'
import mouthIcon from '@/assets/images/openmouth.png'

const stepStore = useStepStore()

// 使用图片上传 composable
const { smileImage, mouthImage, setImage } = useImageUpload()

// 图标路径
const smileIconSrc = smileIcon
const mouthIconSrc = mouthIcon

// 是否可以进入下一步
const canProceed = computed(() => smileImage.value !== '' && mouthImage.value !== '')

// 不再需要监听路由变化，因为 composable 会自动处理图片状态

// 处理微笑照片上传成功
const handleSmileUploadSuccess = (imageData: string) => {
  // 更新本地状态和store
  setImage('smile', imageData)
  ElMessage.success('微笑照片上传成功')
}

// 处理开口照片上传成功
const handleMouthUploadSuccess = (imageData: string) => {
  // 更新本地状态和store
  setImage('mouth', imageData)
  ElMessage.success('开口照片上传成功')
}

// 处理上传错误
const handleUploadError = (errorMessage: string) => {
  ElMessage.error(errorMessage)
}

// 当两张照片都上传完成后，自动更新步骤状态
// 这样用户点击顶部步骤栏时，下一步会被激活
watch(canProceed, (newValue) => {
  if (newValue) {
    // 更新步骤状态，使下一步可点击
    stepStore.setActiveIndex(0) // 当前步骤
    // 注意：不要在这里自动导航，让用户自己点击步骤栏
  }
})
</script>

<style lang="less" scoped>
.import-image-container {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f7f7f7;
}

.import-header {
  text-align: center;
  margin-top: 10px;
}

.import-title {
  font-size: 24px;
  color: #333;
  margin-bottom: 10px;
  font-weight: normal;
}

.import-desc {
  font-size: 16px;
  color: #666;
}

.upload-area {
  display: flex;
  justify-content: center;
  gap: 60px;
  width: 100%;
  max-width: 1400px;
  margin-bottom: 20px;
  margin-top: 20px;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }
}

.upload-box {
  width: 100%;
  max-width: 450px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.upload-content {
  width: 100%;
  height: 450px;
  margin-bottom: 15px;

  :deep(.image-uploader) {
    height: 100%;
    border-radius: 4px;
    background-color: #f5f7fa;

    .upload-icon {
      .icon-image {
        width: 120px;
        height: 120px;
        object-fit: contain;
      }
    }

    &.has-image .preview-image {
      border-radius: 4px;
    }
  }
}

.upload-label {
  text-align: center;
  font-size: 14px;
  color: #666;
  margin-top: 10px;
  padding: 5px 0;
}

.action-tip {
  text-align: center;
  margin-top: 10px;
  padding: 15px;
  background-color: #ecf5ff;
  border-radius: 4px;
  width: 100%;
  max-width: 600px;

  p {
    font-size: 16px;
    color: #409eff;
    margin: 0;
  }
}
</style>
