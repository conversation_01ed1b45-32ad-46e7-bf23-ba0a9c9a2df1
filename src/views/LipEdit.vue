<template>
  <div class="step-page">
    <div
      class="lip-editor-canvas"
      @wheel.prevent="handleWheel"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @mouseleave="handleMouseUp"
    >
      <!-- 重设唇线按钮 -->
      <div
        v-if="imageObj && lipPoints && lipPoints.length > 1"
        class="reset-lip-btn"
        @click="resetLipPoints"
      >
        重设唇线
        <el-icon style="margin-left: 8px">
          <RefreshLeft />
        </el-icon>
      </div>

      <!-- 提示信息 -->
      <div v-if="imageObj && (!lipPoints || lipPoints.length === 0)" class="lip-tip">
        请在图片上点击添加唇线点位
      </div>

      <!-- 添加点位提示 -->
      <div
        v-if="imageObj && lipPoints && (lipPoints.length === 1 || lipPoints.length === 2)"
        class="lip-tip"
      >
        请继续点击添加更多点位来形成唇线
      </div>

      <!-- 闭合曲线提示 -->
      <div
        v-if="imageObj && lipPoints && lipPoints.length >= 3 && !closed"
        class="lip-tip lip-tip-secondary"
      >
        提示：点击第一个点可以闭合曲线
      </div>
      <v-stage
        v-if="imageObj"
        :config="{ width: stageWidth, height: stageHeight }"
        @click="handleStageClick"
      >
        <v-layer>
          <!-- 主图片渲染，自动缩放/偏移/旋转 -->
          <v-image
            :image="imageObj"
            :x="imgCenter.x"
            :y="imgCenter.y"
            :offsetX="imageObj.width / 2"
            :offsetY="imageObj.height / 2"
            :scaleX="imgScale"
            :scaleY="imgScale"
            :rotation="(rotation * 180) / Math.PI"
          />
          <!-- 唇线编辑曲线 -->
          <SplineCurveEditor
            v-if="lipPoints && lipPoints.length > 0"
            v-model:points="transformedLipPoints"
            v-model:closed="closed"
            :width="stageWidth"
            :height="stageHeight"
            :editable="editable"
            :curveColor="'#2080ff'"
            :curveWidth="3"
            :pointRadius="4"
            :pointColor="'#6b9aff'"
            :firstPointColor="'#ff6b6b'"
            :pointStroke="'#FFF'"
            :pointStrokeWidth="2"
            :highlightedPointStrokeWidth="2"
            :allowAddPoints="true"
            :allowAddPointsWhenClosed="true"
            :allowDragPoints="true"
            :detectThreshold="20"
            :scale="imgScale / baseScale"
            @activate="handleActivate"
          />
        </v-layer>
      </v-stage>
      <div v-else class="loading">加载中...</div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useCommonStore } from '@/store/common'
import { storeToRefs } from 'pinia'
import SplineCurveEditor from '@/components/lip/SplineCurveEditor.vue'
import { ElIcon } from 'element-plus'
import { RefreshLeft } from '@element-plus/icons-vue'
import {
  TransformParams,
  calcLipFocusScaleAndOffset as calcLipFocusScaleAndOffsetUtil
} from '@/utils/transform2d'
import { useImageTransformByWheel } from '@/composables/useImageTransformByWheel'
import { useLipEdit } from '@/composables/useLipEdit'

// pinia 仓库
const commonStore = useCommonStore()
const { smileImage, faceRotation, headerHeight } = storeToRefs(commonStore)

// 画布宽高，宽度为窗口宽度，高度为窗口高度减去顶部导航等
const stageWidth = ref(window.innerWidth)
const stageHeight = ref(window.innerHeight - headerHeight.value)

// 图片对象及其原始尺寸
const imageObj = ref<HTMLImageElement | null>(null)
const imgNaturalWidth = ref(0)
const imgNaturalHeight = ref(0)
// const imgScale = ref(1)
// const baseScale = ref(1)
// imgCenter 表示图片在画布坐标系中的渲染位置
// 这里的 x/y 实际是图片左上角在画布坐标系中的位置
const imgCenter = ref({ x: 0, y: 0 })
const baseScale = ref(1)

const rotation = faceRotation

// 统一缩放/拖拽逻辑
const { imgScale, offsetX, offsetY, handleWheel, handleMouseDown, handleMouseMove, handleMouseUp } =
  useImageTransformByWheel({
    minScale: 0.05,
    maxScale: 3,
    scaleStep: 1.1,
    getImageSize: () => ({ width: imgNaturalWidth.value, height: imgNaturalHeight.value }),
    onTransformUpdate: () => {
      imgCenter.value = {
        x: offsetX.value + (imgNaturalWidth.value * imgScale.value) / 2,
        y: offsetY.value + (imgNaturalHeight.value * imgScale.value) / 2
      }

      // 当用户手动缩放时，如果没有唇线点或只有一个点，同步更新 baseScale
      // 这样可以确保在用户添加第一个点之前，baseScale 已经被正确设置
      if (!lipPoints.value || lipPoints.value.length <= 1) {
        baseScale.value = imgScale.value
      }
    }
  })

// 构造通用变换参数
function getTransformParams(): TransformParams {
  return {
    scale: imgScale.value,
    offsetX: offsetX.value,
    offsetY: offsetY.value,
    rotation: faceRotation.value,
    anchorX: imgNaturalWidth.value / 2,
    anchorY: imgNaturalHeight.value / 2
  }
}

// 使用唇线编辑 composable
const {
  lipPoints,
  transformedLipPoints,
  closed,
  editable,
  resetLipPoints,
  initLipPoints,
  activateEdit,
  deactivateEdit,
  stageToImage,
  addPoint,
  setClosedState
} = useLipEdit(getTransformParams)

function handleActivate() {
  activateEdit()
}

// 点击画布处理
function handleStageClick(e: any) {
  // 如果没有唇线点或唇线点数量不足，则添加新点
  if (!lipPoints.value || lipPoints.value.length < 2) {
    // 获取点击位置（相对于stage）
    const stage = e.target.getStage()
    const pointerPosition = stage.getPointerPosition()

    // 将舞台坐标转换为图片坐标
    const imagePoint = stageToImage({
      x: pointerPosition.x,
      y: pointerPosition.y
    })

    // 使用 useLipEdit 提供的方法添加点
    addPoint(imagePoint)

    // 在添加第一个点时，更新 baseScale 为当前的 imgScale
    // 这样可以确保曲线和点的大小与当前缩放比例一致
    baseScale.value = imgScale.value

    activateEdit()

    return
  }

  // 如果已有唇线点且唇线闭合，则取消激活
  deactivateEdit()
}

// 计算变换参数，在加载完成、窗口尺寸变化时调用
function updateImageAndFocus() {
  if (!smileImage.value) return

  // 如果唇线点不存在或数量不足，使用默认居中显示
  const hasValidLipPoints = lipPoints.value && lipPoints.value.length >= 2

  // 如果图片已经加载，直接重新计算变换参数
  if (imageObj.value && imgNaturalWidth.value > 0 && imgNaturalHeight.value > 0) {
    if (hasValidLipPoints) {
      // 自动聚焦唇线，计算变换参数
      const {
        scale,
        offsetX: ox,
        offsetY: oy
      } = calcLipFocusScaleAndOffsetUtil(lipPoints.value, stageWidth.value, stageHeight.value, {
        targetRatio: 0.4,
        rotation: faceRotation.value,
        imgWidth: imgNaturalWidth.value,
        imgHeight: imgNaturalHeight.value
      })
      imgScale.value = scale
      baseScale.value = scale // 记录基准缩放
      offsetX.value = ox
      offsetY.value = oy
    } else {
      // 如果没有有效的唇线点，则居中显示整个图片
      const scale = Math.min(
        (stageWidth.value / imgNaturalWidth.value) * 0.8,
        (stageHeight.value / imgNaturalHeight.value) * 0.8
      )
      imgScale.value = scale
      baseScale.value = scale
      offsetX.value = (stageWidth.value - imgNaturalWidth.value * scale) / 2
      offsetY.value = (stageHeight.value - imgNaturalHeight.value * scale) / 2
    }

    imgCenter.value = {
      x: offsetX.value + (imgNaturalWidth.value * imgScale.value) / 2,
      y: offsetY.value + (imgNaturalHeight.value * imgScale.value) / 2
    }
    return
  }

  // 如果图片未加载，先加载图片
  const img = new window.Image()
  img.src = smileImage.value
  img.onload = () => {
    imageObj.value = img
    imgNaturalWidth.value = img.width
    imgNaturalHeight.value = img.height

    if (hasValidLipPoints) {
      // 自动聚焦唇线，计算变换参数
      const {
        scale,
        offsetX: ox,
        offsetY: oy
      } = calcLipFocusScaleAndOffsetUtil(lipPoints.value, stageWidth.value, stageHeight.value, {
        targetRatio: 0.4,
        rotation: faceRotation.value,
        imgWidth: imgNaturalWidth.value,
        imgHeight: imgNaturalHeight.value
      })
      imgScale.value = scale
      baseScale.value = scale // 记录基准缩放
      offsetX.value = ox
      offsetY.value = oy
    } else {
      // 如果没有有效的唇线点，则居中显示整个图片
      const scale = Math.min(
        (stageWidth.value / imgNaturalWidth.value) * 0.8,
        (stageHeight.value / imgNaturalHeight.value) * 0.8
      )
      imgScale.value = scale
      baseScale.value = scale
      offsetX.value = (stageWidth.value - imgNaturalWidth.value * scale) / 2
      offsetY.value = (stageHeight.value - imgNaturalHeight.value * scale) / 2
    }

    imgCenter.value = {
      x: offsetX.value + (imgNaturalWidth.value * imgScale.value) / 2,
      y: offsetY.value + (imgNaturalHeight.value * imgScale.value) / 2
    }
  }
}

onMounted(async () => {
  // 先检查唇线点是否存在，如果不存在则初始化
  if (!lipPoints.value || lipPoints.value.length === 0) {
    await initLipPoints(stageWidth.value, stageHeight.value)
  }

  // 确保唇线点存在后再聚焦
  if (lipPoints.value && lipPoints.value.length > 1) {
    // 如果是从AI获取的点位数据，默认闭合曲线
    // 这里假设如果初始就有点位数据，那么是从AI获取的
    setClosedState(true)

    // 使用setTimeout确保DOM完全渲染后再聚焦
    setTimeout(() => {
      updateImageAndFocus()
    }, 100)
  } else {
    // 如果没有点位数据或只有一个点，默认不闭合
    setClosedState(false)
    updateImageAndFocus()
  }

  // 监听窗口大小变化，更新画布尺寸
  window.addEventListener('resize', () => {
    stageWidth.value = window.innerWidth
    stageHeight.value = window.innerHeight - headerHeight.value
    updateImageAndFocus()
  })
})

// 监听旋转角度变化，重新计算变换参数
watch(faceRotation, () => {
  updateImageAndFocus()
})
</script>
<style scoped>
.lip-editor-canvas {
  width: 100vw;
  background: #fff;
  position: relative;
}
.loading {
  width: 100%;
  height: 100%;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  color: #aaa;
}
.reset-lip-btn {
  position: absolute;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px #0002;
  padding: 8px 18px 8px 14px;
  font-size: 16px;
  color: #222;
  cursor: pointer;
  display: flex;
  align-items: center;
  z-index: 10;
  user-select: none;
  transition: box-shadow 0.2s;

  &:hover {
    box-shadow: 0 4px 10px #0003;
  }
  &:active {
    box-shadow: 0 2px 4px #0003;
    opacity: 0.8;
  }
}

.lip-tip {
  position: absolute;
  top: 24px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 12px 20px;
  font-size: 16px;
  color: #333;
  z-index: 10;
  user-select: none;
  text-align: center;
  max-width: 90%;
}

.lip-tip-secondary {
  top: 90px;
  background: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  padding: 8px 16px;
  color: #666;
}
</style>
