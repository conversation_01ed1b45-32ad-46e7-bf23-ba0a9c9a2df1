<template>
  <!--
    人脸标注主页面：
    - 主画布用 v-stage/v-layer 渲染图片、点、辅助线
    - 图片以中线为中心居中显示，支持旋转
    - 点位可拖动，hover/拖动高亮
    - 放大镜为 DOM 层圆形，内容为主画布快照，跟随鼠标
  -->
  <div class="step-page">
    <div class="face-editor">
      <div
        class="canvas-area"
        @mousemove="onCanvasMouseMove"
        @wheel="handleWheel"
        @mousedown="handleMouseDown"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseUp"
      >
        <v-stage ref="stageRef" :config="{ width: stageWidth, height: stageHeight }">
          <v-layer>
            <!--
              主图片渲染：
              - offsetX/offsetY 定义图片的原点（锚点）。这里设为图片中心，实现以中心为基准旋转（它跟画布没有关系，跟下面脚本中的offsetX/offsetY 也不是相同的概念）
              - x/y 定义图片的原点（上面定义的offsetX/offsetY使得其为图片的中心），在画布坐标系下的位置。
              - scaleX/scaleY 控制自适应缩放
              - rotation 控制旋转（单位为角度）·
            -->
            <v-image
              v-if="imageObj"
              :image="imageObj"
              :x="imgCenter.x"
              :y="imgCenter.y"
              :offsetX="imageObj.width / 2"
              :offsetY="imageObj.height / 2"
              :scaleX="imgScale"
              :scaleY="imgScale"
              :rotation="(rotation * 180) / Math.PI"
            />
            <!-- 左右眼连线 -->
            <v-line
              v-if="leftEyeStage && rightEyeStage"
              :points="[leftEyeStage.x, leftEyeStage.y, rightEyeStage.x, rightEyeStage.y]"
              stroke="#00c853"
              :strokeWidth="2"
            />
            <!-- 中线（始终垂直，x=midlineStageX） -->
            <v-line
              :points="[midlineStageX, 0, midlineStageX, stageHeight]"
              stroke="#ff6b6b"
              :strokeWidth="2"
              :dash="[8, 6]"
            />
            <!-- 关键点渲染：可拖动，hover/拖动高亮 -->
            <v-circle
              v-for="(pt, idx) in smileFaceLandmarks"
              :key="pt.name"
              :config="{
                ...imageToStage(pt),
                radius: 6 * (imgScale / baseScale),
                fill: idx === activeIdx ? '#0056b3' : '#2080ff',
                stroke: '#fff',
                strokeWidth: 1,
                draggable: true
              }"
              @dragmove="(e: KonvaEventObject<DragEvent>) => handleDragPointMove(e, idx)"
              @mouseover="(e: KonvaEventObject<MouseEvent>) => handlePointHover(e, idx)"
              @mouseout="handlePointOut"
              @dragstart="(e: KonvaEventObject<DragEvent>) => handlePointHover(e, idx)"
              @dragend="handlePointOut"
            />
          </v-layer>
        </v-stage>
        <!--
          放大镜：
          - DOM 层绝对定位圆形
          - 内容为主画布快照（toDataURL）
          - 跟随鼠标移动，内部有十字线辅助定位
        -->
        <div
          v-if="showMagnifier && magnifierImgUrl"
          class="magnifier"
          :style="{ left: magnifierPos.x + 'px', top: magnifierPos.y + 'px' }"
        >
          <img :src="magnifierImgUrl" :width="magnifierSize" :height="magnifierSize" />
          <div class="crosshair"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useCommonStore } from '@/store/common'
import { storeToRefs } from 'pinia'
import { calcMidlineXOnStage } from '@/utils/faceMath'
import type { KonvaEventObject } from 'konva/lib/Node'
import {
  imageToStage as imageToStageUtil,
  stageToImage as stageToImageUtil,
  TransformParams
} from '@/utils/transform2d'
import { useImageTransformByWheel } from '@/composables/useImageTransformByWheel'
import { useFaceData } from '@/composables/useFaceData'

/*
  坐标体系与变换说明：
  --------------------------------------
  注意：图片上设置了offsetX/offsetY分别为图片宽/高的一半，实现图片的原点（锚点）变成图片中心。它是konva的属性，跟下面脚本中的offsetX/offsetY不是相同的概念。

  1. 图片坐标系：
     - 点位（facePoints）原始数据，x/y 均为图片像素坐标，原点为图片左上角。

  2. 画布坐标系：
     - Konva 渲染区域，原点为画布左上角。
     - 图片在画布中通常会有缩放（scale）、偏移（offsetX/offsetY）、旋转（rotation）。

  3. 变换流程：
     - 图片渲染时，先缩放（scale），再偏移（offsetX/offsetY），最后以中心点旋转（rotation）。
     - 缩放的方式是：图片的宽/高 分别除以 画布的宽/高，取最小值（以便完整显示）得到缩放比例。
     - 偏移的方式是：offsetX/offsetY 图片"左上角"在画布上的"左上角"偏移量。初始化是图片缩放后并居中后，相对画布的偏移量，计算方式是：(画布宽/高 - 图片宽/高) / 2。
     - 偏移方式补充：
       - 用户拖动画布时，offsetX/offsetY 会变化，从而使imgCenter变动，它代表图片的中心，从而在画布上移动。
     - 旋转的方式是：以图片中心点为中心，旋转 rotation 弧度。
     - 图片中心点（imgCenter）是画布坐标系下的中心点，也是旋转的中心点。
     - 点位、辅助线等元素，渲染时需做同样的变换，才能与图片对齐。

  4. 关键变换函数：
     - imageToStage(pt):
         - 输入：图片坐标系下的点 {x, y}
         - 输出：画布坐标系下的点 {x, y}
         - 步骤：先缩放+偏移，再以图片中心为中心旋转
     - stageToImage(pos):
         - 输入：画布坐标系下的点 {x, y}
         - 输出：图片坐标系下的点 {x, y}
         - 步骤：先以图片中心逆旋转，再逆缩放+逆偏移

  5. 中线与辅助线：
     - 中线在画布上的位置： midlineStageX，他的计算方式是：先将各关键点转换到画布坐标系，再计算中点。
     - 辅助线（如左右眼连线）两端点都需 imageToStage 变换

  6. 拖动点位：
     - 拖动时获取画布坐标，需用 stageToImage 逆变换回图片坐标系，才能正确更新点位数据

  7. 这样保证：
     - 点位、辅助线、图片始终在视觉上严格对齐
     - 支持任意缩放、旋转、偏移，且所有交互都在图片坐标系下存储
*/

// 1. 取 store 状态
const commonStore = useCommonStore()
const { smileImage, headerHeight, hasManuallyAdjustedFacePoints } = storeToRefs(commonStore)

// 使用面部数据管理 composable
const { smileFaceLandmarks, rotation, setSmileFaceLandmarks } = useFaceData()

// 3. 画布和图片尺寸、缩放
const stageWidth = ref(window.innerWidth)
const stageHeight = ref(window.innerHeight - headerHeight.value)
const imageObj = ref<HTMLImageElement | null>(null)
const imgNaturalWidth = ref(0)
const imgNaturalHeight = ref(0)

// 8. 标记用户是否手动缩放过
const isUserScaling = ref(false)

// 恢复 stageRef 的定义
const stageRef = ref<any>(null)

// 统一缩放/拖拽逻辑
const { imgScale, offsetX, offsetY, handleWheel, handleMouseDown, handleMouseMove, handleMouseUp } =
  useImageTransformByWheel({
    minScale: 0.01,
    maxScale: 5,
    scaleStep: 1.1,
    getImageSize: () => ({ width: imgNaturalWidth.value, height: imgNaturalHeight.value }),
    onTransformUpdate: () => {
      // 缩放/拖拽后同步 imgCenter
      imgCenter.value = {
        x: offsetX.value + (imgNaturalWidth.value * imgScale.value) / 2,
        y: offsetY.value + (imgNaturalHeight.value * imgScale.value) / 2
      }
    }
  })

const baseScale = ref(1) // 记录自适应时的imgScale

// 4. 图片在画布坐标系下的中心点坐标 {x, y}。是旋转的中心点
const imgCenter = ref({ x: 0, y: 0 })

// 6. 关键点辅助计算
const leftEye = computed(() => smileFaceLandmarks.value.find((p) => p.name === 'leftEye'))
const rightEye = computed(() => smileFaceLandmarks.value.find((p) => p.name === 'rightEye'))

// 7. 画布自适应
function updateStageSize() {
  stageWidth.value = window.innerWidth
  stageHeight.value = window.innerHeight - headerHeight.value
  // 居中
  if (imageObj.value) {
    offsetX.value = (stageWidth.value - imageObj.value.width * imgScale.value) / 2
    offsetY.value = (stageHeight.value - imageObj.value.height * imgScale.value) / 2
    imgCenter.value = {
      x: offsetX.value + (imgNaturalWidth.value * imgScale.value) / 2,
      y: offsetY.value + (imgNaturalHeight.value * imgScale.value) / 2
    }
  }
}

// 9. 图片加载，记录原始宽高
watch(
  () => smileImage.value,
  (url) => {
    if (url) {
      const img = new window.Image()
      img.src = url
      img.onload = () => {
        imageObj.value = img
        imgNaturalWidth.value = img.width
        imgNaturalHeight.value = img.height
        // 图片切换时自动自适应
        isUserScaling.value = false
        if (img.width && img.height) {
          // 计算自适应缩放和居中
          const scaleX = stageWidth.value / img.width
          const scaleY = stageHeight.value / img.height
          const scale = Math.min(scaleX, scaleY, 1)
          imgScale.value = scale
          baseScale.value = scale // 记录基准
          offsetX.value = (stageWidth.value - img.width * imgScale.value) / 2
          offsetY.value = (stageHeight.value - img.height * imgScale.value) / 2
          imgCenter.value = {
            x: offsetX.value + (imgNaturalWidth.value * imgScale.value) / 2,
            y: offsetY.value + (imgNaturalHeight.value * imgScale.value) / 2
          }
        }
      }
    }
  },
  { immediate: true }
)

// 10. 图片缩放自适应
watch([imageObj, stageWidth, stageHeight], ([img, w, h]) => {
  if (img && !isUserScaling.value) {
    const scaleX = w / img.width
    const scaleY = h / img.height
    const scale = Math.min(scaleX, scaleY, 1)
    imgScale.value = scale
    baseScale.value = scale // 记录基准
    // 缩放后居中
    offsetX.value = (stageWidth.value - img.width * imgScale.value) / 2
    offsetY.value = (stageHeight.value - img.height * imgScale.value) / 2
    imgCenter.value = {
      x: offsetX.value + (imgNaturalWidth.value * imgScale.value) / 2,
      y: offsetY.value + (imgNaturalHeight.value * imgScale.value) / 2
    }
  }
})

// 11. 页面挂载时初始化
onMounted(() => {
  updateStageSize()
  window.addEventListener('resize', updateStageSize)
  // 注意：AI检测数据已在ImportImage页面获取，这里不再重复获取
})

onUnmounted(() => {
  window.removeEventListener('resize', updateStageSize)
})

// 12. 坐标变换工具函数
// 旋转点（以 center 为中心，angle 弧度）
// 已由工具库提供

// 构造通用变换参数
function getTransformParams(): TransformParams {
  return {
    scale: imgScale.value,
    offsetX: offsetX.value,
    offsetY: offsetY.value,
    rotation: rotation.value,
    anchorX: imgNaturalWidth.value / 2,
    anchorY: imgNaturalHeight.value / 2
  }
}
// 图片坐标 -> 画布坐标（缩放+偏移+旋转）
function imageToStage(pt: { x: number; y: number }) {
  return imageToStageUtil(pt, getTransformParams())
}
// 画布坐标 -> 图片坐标（逆变换）
function stageToImage(pos: { x: number; y: number }) {
  return stageToImageUtil(pos, getTransformParams())
}

// 5. 人脸中线在画布坐标系下的 x 坐标
// 使用新方法：先将各点转换到画布坐标系，再计算中点
const midlineStageX = computed(() => calcMidlineXOnStage(smileFaceLandmarks.value, imageToStage))

// 左右眼在画布坐标系下的位置
const leftEyeStage = computed(() => (leftEye.value ? imageToStage(leftEye.value) : null))
const rightEyeStage = computed(() => (rightEye.value ? imageToStage(rightEye.value) : null))

// 13. 拖动点时，实时更新点位和放大镜
let magnifierRafId: number | null = null
let lastMagnifierIdx: number | null = null
function updateMagnifierRaf(idx: number) {
  lastMagnifierIdx = idx
  if (magnifierRafId !== null) return
  magnifierRafId = requestAnimationFrame(() => {
    if (lastMagnifierIdx !== null) updateMagnifier(lastMagnifierIdx)
    magnifierRafId = null
  })
}

// 拖动点时
function handleDragPointMove(e: KonvaEventObject<DragEvent>, idx: number) {
  const pos = e.target.position()
  const imgPt = stageToImage(pos)
  const old = smileFaceLandmarks.value[idx]
  if (old && old.name) {
    const newPoints = smileFaceLandmarks.value.map((p, i) =>
      i === idx ? { name: old.name, x: imgPt.x, y: imgPt.y } : p
    )
    // console.log('newPoints', newPoints)
    setSmileFaceLandmarks(newPoints)

    // 标记用户已手动调整过关键点
    hasManuallyAdjustedFacePoints.value = true
  }
  // 拖动时用 rAF 节流刷新放大镜
  if (hoverIdx.value === idx) {
    updateMagnifierRaf(idx)
  }
}

// 14. 放大镜相关状态
const hoverIdx = ref<number | null>(null) // 当前 hover/拖动的点索引
const activeIdx = ref<number | null>(null) // 当前高亮点索引
const hoverStagePos = ref<{ x: number; y: number } | null>(null) // 当前 hover 点的画布坐标
const showMagnifier = computed(() => hoverIdx.value !== null && hoverStagePos.value)
const magnifierSize = 300
const magnifierScale = 1.6
const magnifierImgUrl = ref<string | null>(null)
const magnifierPos = ref({ x: 0, y: 0 })

// 生成放大镜快照
async function updateMagnifier(idx: number) {
  await nextTick()
  if (!stageRef.value) return
  const pt = smileFaceLandmarks.value[idx]
  if (!pt) return
  const stagePt = imageToStage(pt)
  const x = stagePt.x - magnifierSize / (2 * magnifierScale)
  const y = stagePt.y - magnifierSize / (2 * magnifierScale)
  const url = stageRef.value.getNode().toDataURL({
    x,
    y,
    width: magnifierSize / magnifierScale,
    height: magnifierSize / magnifierScale,
    pixelRatio: magnifierScale
  })
  magnifierImgUrl.value = url
}
// 悬停/拖动点时，显示放大镜并高亮当前点
function handlePointHover(e: KonvaEventObject<MouseEvent | DragEvent>, idx: number) {
  hoverIdx.value = idx
  activeIdx.value = idx
  const pos = e.target.getAbsolutePosition()
  hoverStagePos.value = pos
  updateMagnifier(idx)
}
// 离开点时，隐藏放大镜和高亮
function handlePointOut(_e?: KonvaEventObject<MouseEvent | DragEvent>) {
  hoverIdx.value = null
  activeIdx.value = null
  hoverStagePos.value = null
  magnifierImgUrl.value = null
}
// 鼠标移动时，放大镜跟随鼠标
function handleMagnifierMove(e: MouseEvent) {
  if (!showMagnifier.value) return
  const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
  let x = e.clientX - rect.left - magnifierSize - 16
  let y = e.clientY - rect.top - magnifierSize - 16
  x = Math.max(8, Math.min(x, stageWidth.value - magnifierSize - 8))
  y = Math.max(8, Math.min(y, stageHeight.value - magnifierSize - 8))
  magnifierPos.value = { x, y }
}

function onCanvasMouseMove(e: MouseEvent) {
  handleMagnifierMove(e)
  handleMouseMove(e)
}
</script>
<style scoped>
.face-editor {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.canvas-area {
  width: 100vw;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.magnifier {
  position: absolute;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  box-shadow: 0 2px 12px #0002;
  border: 2px solid #2080ff;
  pointer-events: none;
  overflow: hidden;
  z-index: 10;
}
.magnifier img {
  width: 100%;
  height: 100%;
  display: block;
}
.crosshair {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}
.crosshair::before,
.crosshair::after {
  content: '';
  position: absolute;
  background: rgba(255, 255, 255, 0.7);
}
.crosshair::before {
  left: 50%;
  top: 0;
  width: 2px;
  height: 100%;
  transform: translateX(-1px);
}
.crosshair::after {
  top: 50%;
  left: 0;
  height: 2px;
  width: 100%;
  transform: translateY(-1px);
}
</style>
