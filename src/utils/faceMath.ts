import type { FacePoint } from '@/types/face'

// 根据点名获取点
function getPoint(points: FacePoint[], name: string) {
  return points.find((p) => p.name === name)
}

// 计算两点连线的倾斜角度
export function calcLineAngle(p1: FacePoint, p2: FacePoint): number {
  if (!p1 || !p2) return 0
  return Math.atan2(p2.y - p1.y, p2.x - p1.x)
}

// 计算图片旋转角度（弧度），通过左右眼的倾斜角度跟嘴巴的倾斜角度计算
export function calcFaceRotation(points: FacePoint[]): number {
  const leftEye = getPoint(points, 'leftEye')
  const rightEye = getPoint(points, 'rightEye')
  const leftMouth = getPoint(points, 'leftMouth')
  const rightMouth = getPoint(points, 'rightMouth')
  if (!leftEye || !rightEye || !leftMouth || !rightMouth) {
    return 0
  }
  const angleEye = calcLineAngle(leftEye, rightEye)
  const angleMouth = calcLineAngle(leftMouth, rightMouth)
  return -(angleEye + angleMouth) / 2
}

// 计算三组关键点中点的X坐标平均值，得到人脸中线（图片坐标系）
export function calcMidlineX(points: FacePoint[]): number {
  const leftEye = getPoint(points, 'leftEye')
  const rightEye = getPoint(points, 'rightEye')
  const leftNostril = getPoint(points, 'leftNostril')
  const rightNostril = getPoint(points, 'rightNostril')
  const leftMouth = getPoint(points, 'leftMouth')
  const rightMouth = getPoint(points, 'rightMouth')

  if (!leftEye || !rightEye || !leftNostril || !rightNostril || !leftMouth || !rightMouth) {
    return 0
  }

  const eyeCenterX = (leftEye.x + rightEye.x) / 2
  const nostrilCenterX = (leftNostril.x + rightNostril.x) / 2
  const mouthCenterX = (leftMouth.x + rightMouth.x) / 2
  return (eyeCenterX + nostrilCenterX + mouthCenterX) / 3
}

/**
 * 计算画布坐标系下的中线X坐标
 * 先将各点转换到画布坐标系，再计算中点，确保在画布上显示的是真正的中线
 * @param points 面部关键点
 * @param imageToStage 图片坐标转画布坐标的函数
 * @returns 画布坐标系下的中线X坐标
 */
export function calcMidlineXOnStage(
  points: FacePoint[],
  imageToStage: (pt: { x: number; y: number }) => { x: number; y: number }
): number {
  const leftEye = getPoint(points, 'leftEye')
  const rightEye = getPoint(points, 'rightEye')
  const leftNostril = getPoint(points, 'leftNostril')
  const rightNostril = getPoint(points, 'rightNostril')
  const leftMouth = getPoint(points, 'leftMouth')
  const rightMouth = getPoint(points, 'rightMouth')

  if (!leftEye || !rightEye || !leftNostril || !rightNostril || !leftMouth || !rightMouth) {
    return 0
  }

  // 将各点转换到画布坐标系
  const leftEyeStage = imageToStage(leftEye)
  const rightEyeStage = imageToStage(rightEye)
  const leftNostrilStage = imageToStage(leftNostril)
  const rightNostrilStage = imageToStage(rightNostril)
  const leftMouthStage = imageToStage(leftMouth)
  const rightMouthStage = imageToStage(rightMouth)

  // 在画布坐标系中计算三组点的中点X坐标
  const eyeCenterX = (leftEyeStage.x + rightEyeStage.x) / 2
  const nostrilCenterX = (leftNostrilStage.x + rightNostrilStage.x) / 2
  const mouthCenterX = (leftMouthStage.x + rightMouthStage.x) / 2

  // 取平均值得到中线X坐标
  return (eyeCenterX + nostrilCenterX + mouthCenterX) / 3
}
