import { Ref, computed, watch, ref, onActivated } from 'vue'
import Konva from 'konva'
import { useTextureManager } from './useTextureManager'
import { SEGMENT_WIDTH_RATIOS_CENTER_OUT } from '@/store/teeth'

/**
 * 牙齿纹理应用composable
 * 处理纹理在牙齿上的应用逻辑
 *
 * @param baseLineRef、textureLineRef 牙齿轮廓线的引用（底层和顶层）
 * @param showTexture 是否显示纹理的标志
 * @param toothId 牙齿ID，用于日志和调试
 * @param baseConfig 基础配置对象的计算属性
 * @param outlinePoints 牙齿轮廓点数组，用于计算牙齿中心点
 * @returns 纹理应用相关的方法和状态
 */

// 纹理映射结果缓存，避免重复计算
const textureMappingCache = new Map<number, { offsetRatio: number; widthRatio: number }>()

/**
 * 计算牙齿在纹理图片中的映射区域
 * 基于牙齿ID和宽度比例计算每颗牙齿应该使用纹理图片中的哪个区域
 *
 * @param toothId 牙齿ID，范围通常是 -5 到 5，不包括 0
 * @returns 纹理映射信息，包括起始位置、宽度比例等
 */
function calculateTextureMapping(toothId: number) {
  // 检查缓存中是否已有计算结果
  if (textureMappingCache.has(toothId)) {
    return textureMappingCache.get(toothId)!
  }

  // 牙齿ID范围通常是 -5 到 5，不包括 0
  // 负数表示左侧牙齿，正数表示右侧牙齿

  // 获取牙齿的绝对索引（1-5）
  const absIndex = Math.abs(toothId)
  if (absIndex === 0 || absIndex > SEGMENT_WIDTH_RATIOS_CENTER_OUT.length) {
    const result = {
      offsetRatio: 0,
      widthRatio: 0.2 // 默认宽度比例
    }
    textureMappingCache.set(toothId, result)
    return result
  }

  // 确定是左侧还是右侧牙齿
  const isLeft = toothId < 0

  // 使用从中心向外的宽度比例数组
  const ratios = SEGMENT_WIDTH_RATIOS_CENTER_OUT

  // 计算所有比例的总和
  const totalRatio = ratios.reduce((sum, ratio) => sum + ratio, 0) * 2 // 左右两侧
  // 根据牙齿ID确定在纹理图片中的位置
  // 纹理图片从左到右对应牙齿从左到右的排列
  // 左侧牙齿ID: -5, -4, -3, -2, -1
  // 右侧牙齿ID: 1, 2, 3, 4, 5

  // 将牙齿ID映射到纹理图片中的位置索引（0-9）
  // 使用正确的映射公式：左侧牙齿是 5-absIndex，右侧牙齿是 absIndex+4
  const textureIndex = isLeft ? 5 - absIndex : absIndex + 4

  // 确保索引在有效范围内（0-9）
  const safeIndex = Math.min(Math.max(textureIndex, 0), 9)

  // 计算当前牙齿的宽度比例（确保索引有效）
  let currentRatio = 0
  if (absIndex <= ratios.length && absIndex > 0) {
    currentRatio = ratios[absIndex - 1] || 0
  } else {
    currentRatio = ratios[0] || 0
  }

  // 空白区域比例
  const BLANK_MARGIN_RATIO = 0.082

  // 有效纹理区域比例（总宽度减去两侧空白）
  const EFFECTIVE_TEXTURE_RATIO = 1 - BLANK_MARGIN_RATIO * 2

  // 计算宽度比例 - 使用SEGMENT_WIDTH_RATIOS_CENTER_OUT计算
  // 这个比例是相对于整个纹理图片宽度的，需要考虑空白区域
  const widthRatio = (currentRatio / totalRatio) * EFFECTIVE_TEXTURE_RATIO
  // 计算起始位置比例 - 这决定了纹理图片中的哪个部分会显示在牙齿上
  // 需要计算当前牙齿之前的所有牙齿的累积宽度比例
  // 计算每个牙齿在纹理中的累积位置
  let cumulativePosition = 0

  // 根据纹理索引计算起始位置
  if (isLeft) {
    // 左侧牙齿 (索引 0-4)
    for (let i = 0; i < safeIndex; i++) {
      // 计算当前位置的牙齿ID
      const currentToothId = -(5 - i)
      const currentAbsIndex = Math.abs(currentToothId)

      // 确保索引有效
      if (currentAbsIndex > 0 && currentAbsIndex <= ratios.length) {
        // 累加当前牙齿的宽度比例
        const ratio = ratios[currentAbsIndex - 1]
        if (ratio !== undefined) {
          cumulativePosition += ratio / totalRatio
        }
      }
    }
  } else {
    // 右侧牙齿 (索引 5-9)
    // 先加上所有左侧牙齿的宽度比例
    for (let i = 0; i < 5; i++) {
      const leftToothAbsIndex = 5 - i
      if (leftToothAbsIndex > 0 && leftToothAbsIndex <= ratios.length) {
        const ratio = ratios[leftToothAbsIndex - 1]
        if (ratio !== undefined) {
          cumulativePosition += ratio / totalRatio
        }
      }
    }

    // 再加上右侧牙齿的累积宽度比例
    for (let i = 5; i < safeIndex; i++) {
      const currentToothId = i - 4
      if (currentToothId > 0 && currentToothId <= ratios.length) {
        const ratio = ratios[currentToothId - 1]
        if (ratio !== undefined) {
          cumulativePosition += ratio / totalRatio
        }
      }
    }
  }

  // 计算起始位置比例，考虑左侧空白和累积位置
  const offsetRatio = BLANK_MARGIN_RATIO + cumulativePosition * EFFECTIVE_TEXTURE_RATIO

  // 缓存计算结果
  const result = { offsetRatio, widthRatio }
  textureMappingCache.set(toothId, result)

  return result
}

/**
 * 牙齿纹理应用composable
 * 处理纹理在牙齿上的应用逻辑
 */
export function useToothTexture(
  baseLineRef: Ref<any>,
  textureLineRef: Ref<any>,
  showTexture: Ref<boolean>,
  toothId: Ref<number>,
  baseConfig: Ref<any>,
  outlinePoints: Ref<number[]>
) {
  // 获取纹理管理器
  const { selectedTextureImage, selectedTextureId, colorAdjustments } = useTextureManager()

  /**
   * 计算牙齿的包围盒
   * 用于确定纹理的位置和缩放
   */
  const toothBoundingBox = computed(() => {
    // 如果没有轮廓点或轮廓点数量不足，返回默认值
    if (!outlinePoints || !outlinePoints.value || outlinePoints.value.length < 4) {
      // 返回一个默认的有效包围盒，避免后续计算出错
      return {
        minX: 0,
        maxX: 10,
        minY: 0,
        maxY: 10,
        width: 10,
        height: 10,
        centerX: 5,
        centerY: 5
      }
    }

    // 初始化边界值
    let minX = Infinity,
      minY = Infinity
    let maxX = -Infinity,
      maxY = -Infinity

    // 遍历所有轮廓点，找出最小和最大的坐标值
    for (let i = 0; i < outlinePoints.value.length; i += 2) {
      const x = outlinePoints.value[i]
      const y = outlinePoints.value[i + 1]

      // 确保坐标值有效
      if (x !== undefined && y !== undefined && !isNaN(x) && !isNaN(y)) {
        minX = Math.min(minX, x)
        minY = Math.min(minY, y)
        maxX = Math.max(maxX, x)
        maxY = Math.max(maxY, y)
      }
    }

    // 计算宽度、高度和中心点
    let width = maxX - minX
    let height = maxY - minY
    const centerX = (minX + maxX) / 2
    const centerY = (minY + maxY) / 2

    return {
      minX,
      maxX,
      minY,
      maxY,
      width,
      height,
      centerX,
      centerY
    }
  })

  /**
   * 创建底层线条配置对象
   * 负责基本颜色和色相/饱和度/亮度调整
   */
  const baseLineConfig = computed(() => {
    // 获取基础配置的副本
    const config = { ...baseConfig.value }

    // 设置完美绘制为false，提高性能
    config.perfectDrawEnabled = false

    // 使用用户选择的牙齿基础颜色（从纹理管理器中获取）
    const baseToothColor = colorAdjustments.value.baseColor || '#FFFAF0'

    // 设置基础填充色
    config.fill = baseToothColor

    // 添加底层线条的基本样式
    config.stroke = 'rgba(233,233,233,0.3)'
    config.strokeWidth = 1.5

    // 如果显示纹理，应用色相/饱和度/亮度调整
    if (showTexture.value) {
      // 添加滤镜相关配置
      const filters = []

      // 添加色相调整滤镜
      filters.push(Konva.Filters.HSL)

      // 设置HSL滤镜的属性
      config.hue = colorAdjustments.value.hue // 色相调整
      config.saturation = colorAdjustments.value.saturation // 饱和度调整
      config.luminance = colorAdjustments.value.brightness // 亮度调整

      // 如果有滤镜，添加到配置中
      if (filters.length > 0) {
        config.filters = filters
      }
    }

    return config
  })

  /**
   * 创建顶层线条配置对象
   * 负责纹理和高光/阴影效果
   */
  const textureLineConfig = computed(() => {
    // 获取基础配置的副本
    const config = { ...baseConfig.value }

    // 设置完美绘制为false，提高性能
    config.perfectDrawEnabled = false

    // 默认无填充，透明状态
    config.fill = 'transparent'
    // config.opacity = 0

    // 如果启用纹理，添加纹理相关配置
    if (showTexture.value && selectedTextureImage.value) {
      // 直接使用selectedTextureImage，这是一个computed属性，确保纹理图片已加载
      const textureImage = selectedTextureImage.value

      if (textureImage) {
        // 使用纹理强度参数控制纹理的不透明度
        const textureStrength =
          colorAdjustments.value.textureStrength !== undefined
            ? colorAdjustments.value.textureStrength
            : 0.7

        // 设置基本可见度
        config.opacity = textureStrength

        // 添加纹理相关配置
        config.fillPatternImage = textureImage // 纹理图片

        // 设置混合模式 - 这是关键，使纹理只影响高光和阴影，而不是完全覆盖底色
        // 'multiply' 会将纹理与底层颜色相乘，暗部更暗
        // 'overlay' 会保留高光和阴影，是个不错的选择
        // 'screen' 会增强亮部
        config.globalCompositeOperation = 'multiply'

        // 添加边缘效果，模拟牙齿质感
        config.stroke = 'rgba(255,255,255,0.45)' // 高亮白色边缘，增强光泽感
        config.strokeWidth = 2 // 细的边缘宽度，模拟自然牙齿边缘

        // 添加内部阴影效果，增强立体感
        config.shadowColor = 'rgba(0,0,0,0.12)' // 淡的黑色阴影
        config.shadowBlur = 8 // 较大的模糊效果，使阴影更加柔和
        config.shadowOffset = { x: 0, y: 1 } // 轻微向下偏移，模拟光源从上方照射
        config.shadowOpacity = 0.5 // 适中的不透明度
        config.shadowEnabled = true // 启用阴影
        config.shadowForStrokeEnabled = false // 不为描边添加阴影

        // 计算牙齿在纹理图片中的映射区域
        const { offsetRatio, widthRatio } = calculateTextureMapping(toothId.value)

        // 使用牙齿包围盒的中心点作为纹理定位点，使纹理跟随牙齿
        const { centerX, centerY, width, height } = toothBoundingBox.value

        // 计算纹理缩放比例
        // 纹理图片需要按照牙齿的宽度比例进行缩放，使每颗牙齿显示对应的纹理部分
        // 计算牙齿应该显示的纹理部分宽度
        const textureWidthForTooth = textureImage.width * widthRatio

        // 计算水平方向的缩放比例
        const scaleX = width / textureWidthForTooth

        // 计算垂直方向的缩放比例
        let scaleY = height / textureImage.height / 0.8

        // 计算纹理在牙齿上的起始位置
        const patternOffsetX = -textureImage.width * offsetRatio * scaleX

        // 设置纹理的定位点 - 这是关键，确保纹理跟随牙齿移动
        config.fillPatternX = centerX + patternOffsetX
        config.fillPatternY = centerY

        // 记录日志，帮助调试
        if (toothId.value === 1) {
          console.log('纹理定位计算', {
            toothId: toothId.value,
            centerX,
            centerY,
            width,
            height,
            patternOffsetX,
            fillPatternX: config.fillPatternX,
            fillPatternY: config.fillPatternY
          })
        }

        // 设置纹理的偏移量（是以原图的尺寸来计算，往左往上），它们的作用是纹理图片相对于自己的坐标进行偏移
        // 这里Y偏移纹理高度的一半，X偏移该颗牙齿对于纹理宽度的一半
        config.fillPatternOffsetX = (widthRatio * textureImage.width) / 2
        config.fillPatternOffsetY = textureImage.height / 2

        // 根据牙齿ID调整垂直缩放和偏移
        const absToothId = Math.abs(toothId.value)
        if (absToothId === 1) {
          scaleY = height / textureImage.height / 0.8
        } else if (absToothId === 2) {
          scaleY = height / textureImage.height / 0.5
        } else if (absToothId === 3) {
          scaleY = height / textureImage.height / 0.6
        } else if (absToothId === 4) {
          scaleY = height / textureImage.height / 0.5
          config.fillPatternOffsetY -= textureImage.height / 12
        } else if (absToothId === 5) {
          scaleY = height / textureImage.height / 0.6
          config.fillPatternOffsetY -= textureImage.height / 9
        }

        // 设置纹理缩放
        config.fillPatternScale = { x: scaleX, y: scaleY }

        // 设置纹理不重复
        config.fillPatternRepeat = 'no-repeat'

        // 设置优先级，确保纹理在其他滤镜之前应用
        config.fillPriority = 'pattern'
      }
    }

    return config
  })

  /**
   * 强制更新Konva节点的纹理配置
   * 用于在计算属性无法触发更新时手动更新
   * 使用缓存优化渲染性能
   */
  function forceUpdateTexture() {
    // 更新底层基础颜色线条
    if (baseLineRef.value) {
      try {
        const baseLine = baseLineRef.value.getNode()
        if (baseLine) {
          baseLine.setAttrs(baseLineConfig.value)
          baseLine.perfectDrawEnabled(false)

          // 计算牙齿的边界框，用于缓存
          const { minX, minY, width, height } = toothBoundingBox.value
          const padding = Math.max(width, height) * 0.1

          // 清除之前的缓存并重新缓存节点
          baseLine.clearCache()
          baseLine.cache({
            x: minX - padding,
            y: minY - padding,
            width: width + padding * 2,
            height: height + padding * 2
          })

          const layer = baseLine.getLayer()
          if (layer) {
            layer.batchDraw()
          }
        }
      } catch (error) {
        console.error(`底层牙齿轮廓 ${toothId.value} - 更新错误:`, error)
      }
    }

    // 更新顶层纹理线条
    if (textureLineRef.value) {
      try {
        const textureLine = textureLineRef.value.getNode()
        if (textureLine) {
          textureLine.setAttrs(textureLineConfig.value)
          textureLine.perfectDrawEnabled(false)

          // 计算牙齿的边界框，用于缓存
          const { minX, minY, width, height } = toothBoundingBox.value
          const padding = Math.max(width, height) * 0.1

          // 清除之前的缓存并重新缓存节点
          textureLine.clearCache()
          textureLine.cache({
            x: minX - padding,
            y: minY - padding,
            width: width + padding * 2,
            height: height + padding * 2
          })

          const layer = textureLine.getLayer()
          if (layer) {
            layer.batchDraw()
          }
        }
      } catch (error) {
        console.error(`纹理牙齿轮廓 ${toothId.value} - 更新错误:`, error)
      }
    }
  }

  // 创建一个缓存状态标志
  const needsTextureUpdate = ref(false)

  // 使用requestAnimationFrame优化更新
  const updateWithRAF = () => {
    if (needsTextureUpdate.value) {
      forceUpdateTexture()
      needsTextureUpdate.value = false
    }
  }

  // 请求动画帧更新函数
  const requestUpdate = () => {
    if (!needsTextureUpdate.value) {
      needsTextureUpdate.value = true
      window.requestAnimationFrame(updateWithRAF)
    }
  }

  // 监听纹理图像变化 - 立即更新
  watch(selectedTextureImage, () => {
    forceUpdateTexture() // 纹理图像变化时立即更新，这是重要的视觉变化
  })

  // 监听纹理ID变化 - 立即更新
  watch(selectedTextureId, () => {
    forceUpdateTexture() // 纹理ID变化时立即更新，这是重要的视觉变化
  })

  // 监听显示纹理标志变化 - 立即更新
  watch(showTexture, () => {
    forceUpdateTexture() // 显示状态变化时立即更新，这是重要的视觉变化
  })

  // 监听颜色调整变化 - 使用RAF优化
  watch(
    colorAdjustments,
    () => {
      requestUpdate() // 使用RAF优化，避免频繁更新
    },
    { deep: true }
  )

  // 监听牙齿包围盒变化 - 使用RAF优化
  watch(
    toothBoundingBox,
    () => {
      if (showTexture.value && selectedTextureImage.value) {
        requestUpdate() // 使用RAF优化，避免频繁更新
      }
    },
    { deep: true }
  )

  // 在组件挂载时预加载纹理并应用缓存
  onActivated(() => {
    // 使用requestAnimationFrame确保DOM已完全渲染
    window.requestAnimationFrame(() => {
      // 初始应用纹理
      forceUpdateTexture()

      // 处理底层线条
      if (baseLineRef.value) {
        const baseLine = baseLineRef.value.getNode()
        if (baseLine) {
          baseLine.listening(false)

          // 应用缓存，提高渲染性能
          if (!baseLine.isCached()) {
            const { minX, minY, width, height } = toothBoundingBox.value
            if (width > 0 && height > 0) {
              const padding = Math.max(width, height) * 0.1
              try {
                baseLine.cache({
                  x: minX - padding,
                  y: minY - padding,
                  width: width + padding * 2,
                  height: height + padding * 2
                })
              } catch (error) {
                console.error(`底层牙齿轮廓 ${toothId.value} - 缓存失败:`, error)
              }
            }
          }
        }
      }

      // 处理顶层线条
      if (textureLineRef.value) {
        const textureLine = textureLineRef.value.getNode()
        if (textureLine) {
          textureLine.listening(false)

          // 应用缓存，提高渲染性能
          if (!textureLine.isCached() && showTexture.value) {
            const { minX, minY, width, height } = toothBoundingBox.value
            if (width > 0 && height > 0) {
              const padding = Math.max(width, height) * 0.1
              try {
                textureLine.cache({
                  x: minX - padding,
                  y: minY - padding,
                  width: width + padding * 2,
                  height: height + padding * 2
                })
              } catch (error) {
                console.error(`纹理牙齿轮廓 ${toothId.value} - 缓存失败:`, error)
              }
            }
          }
        }
      }
    })
  })

  // 返回纹理相关的方法和状态
  return {
    // 线条配置
    baseLineConfig,
    textureLineConfig,

    // 原始lineConfig保留，以保持向后兼容
    lineConfig: textureLineConfig,

    // 其他计算属性
    toothBoundingBox,

    // 方法
    forceUpdateTexture, // 返回完整版本的更新函数，以便外部可以直接调用
    requestUpdate, // 返回RAF优化版本的更新请求函数

    // 状态
    selectedTextureImage,
    selectedTextureId,
    colorAdjustments,
    needsTextureUpdate
  }
}
