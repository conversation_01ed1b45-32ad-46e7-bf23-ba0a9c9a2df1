import { ref, watchEffect } from 'vue'
import { useCommonStore } from '@/store/common'
import { useFrameRelativePositionStore } from '@/store/frameRelativePosition'
import { storeToRefs } from 'pinia'
import { useCanvasStage } from '@/composables/veneer/useCanvasStage'
import { useImageLoader } from '@/composables/veneer/useImageLoader'
import { useImageTransform } from '@/composables/veneer/useImageTransform'
import { useImageLoadCoordinator } from '@/composables/veneer/useImageLoadCoordinator'
import { provideTextureEditorContext } from '@/contexts/TextureEditorContext'

/**
 * 提供纹理编辑器上下文的组合式函数
 * 专注于纹理页面所需的功能
 */
export function useTextureEditorProvider() {
  // 从store获取数据
  const commonStore = useCommonStore()
  const frameRelativePositionStore = useFrameRelativePositionStore()
  const { faceRotation, headerHeight } = storeToRefs(commonStore)

  // 1. 画布舞台管理
  const { stageWidth, stageHeight, updateStageSize: updateStageSizeBase } = useCanvasStage()

  // 2. 图片加载管理 - 纹理页面只需要微笑照
  const { smileImgObj, smileImgNaturalWidth, smileImgNaturalHeight } = useImageLoader()

  // 3. 图像变换管理
  const {
    imgScale,
    imgCenter,
    getTransformParams,
    autoFocusToTeethArea,
    handleWheel,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    resetInteraction: resetInteractionBase
  } = useImageTransform(stageWidth, stageHeight, smileImgNaturalWidth, smileImgNaturalHeight)

  // 局部框架状态
  const localFrameX = ref(0)
  const localFrameY = ref(0)
  const localFrameWidth = ref(0)
  const localFrameHeight = ref(0)
  const localFrameTopCurvePoints = ref({ leftY: 0, midY: 0, rightY: 0 })
  const localFrameBottomCurvePoints = ref({ leftY: 0, midY: 0, rightY: 0 })
  const hasInitializedLocalFrame = ref(false)

  // 更新局部框架位置
  function updateLocalFramePosition() {
    // 如果框架相对位置已初始化，则使用相对位置计算局部框架位置
    if (frameRelativePositionStore.initialized) {
      // 计算图片的缩放后尺寸
      const imgWidth = smileImgNaturalWidth.value * imgScale.value
      const imgHeight = smileImgNaturalHeight.value * imgScale.value

      // 计算框架的绝对位置
      const framePosition = frameRelativePositionStore.calculateAbsolutePosition(
        imgCenter.value.x,
        imgCenter.value.y,
        imgWidth,
        imgHeight
      )

      // 只更新局部框架位置，不影响全局TeethStore
      localFrameX.value = framePosition.x
      localFrameY.value = framePosition.y
      localFrameWidth.value = framePosition.width
      localFrameHeight.value = framePosition.height
      localFrameTopCurvePoints.value = framePosition.topCurvePoints
      localFrameBottomCurvePoints.value = framePosition.bottomCurvePoints
    }
  }

  function resetInteraction() {
    resetInteractionBase()
  }

  // 使用图片加载协调器，处理图片加载完成后的自动聚焦
  useImageLoadCoordinator(smileImgObj, autoFocusToTeethArea)

  // 监听imgScale和imgCenter的变化，更新局部框架位置
  watchEffect(() => {
    updateLocalFramePosition()
  })

  // 包装updateStageSize，添加自动聚焦功能
  function updateStageSize(container: HTMLElement | null) {
    updateStageSizeBase(container, () => {
      // 当舞台尺寸变化时，自动聚焦到牙齿区域
      autoFocusToTeethArea()
    })
  }

  // 初始化局部框架
  function initLocalFrame(frameData: any) {
    localFrameX.value = frameData.x
    localFrameY.value = frameData.y
    localFrameWidth.value = frameData.width
    localFrameHeight.value = frameData.height
    localFrameTopCurvePoints.value = frameData.topCurvePoints
    localFrameBottomCurvePoints.value = frameData.bottomCurvePoints
    hasInitializedLocalFrame.value = true
  }

  // 纹理特有状态
  const showMidline = ref(false)
  const showSmileFrame = ref(false)

  // 提供纹理编辑器上下文
  provideTextureEditorContext({
    // 画布渲染相关状态
    stageWidth,
    stageHeight,
    smileImgObj,
    smileImgNaturalWidth,
    smileImgNaturalHeight,
    imgScale,
    imgCenter,
    faceRotation,
    headerHeight,

    // 纹理特有状态
    showMidline,
    showSmileFrame,

    // 局部框架状态
    localFrameX,
    localFrameY,
    localFrameWidth,
    localFrameHeight,
    localFrameTopCurvePoints,
    localFrameBottomCurvePoints,
    hasInitializedLocalFrame,

    // 画布交互方法
    getTransformParams,
    autoFocusToTeethArea,
    handleWheel,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    updateStageSize,
    resetInteraction,

    // 局部框架方法
    updateLocalFramePosition,
    initLocalFrame
  })

  // 返回一些可能在模板中直接使用的值
  return {
    headerHeight,
    stageWidth,
    stageHeight,
    smileImgObj
  }
}
