import { ref, computed, reactive } from 'vue'
import { useTextureStore } from '@/store/texture'
import { storeToRefs } from 'pinia'

// 导入纹理图片
import amberTexture from '@/assets/textures/_Amber_.bmp'
import druzyTexture from '@/assets/textures/_Druzy_.bmp'
import emeraldTexture from '@/assets/textures/_Emerald_.bmp'
import fab1Texture from '@/assets/textures/_FAB_1_05_Atif_.bmp'
import fab2Texture from '@/assets/textures/_FAB_1_06_Elinor_.bmp'
import graniteTexture from '@/assets/textures/_Grahnite_Bright_.bmp'

/**
 * 纹理信息接口
 */
export interface TextureInfo {
  id: string
  name: string
  src: string
}

// 创建单例缓存，确保在不同组件间共享纹理图片
// 使用非响应式对象存储图片，避免Vue的响应式系统带来的性能开销
const textureImagesCache: Record<string, HTMLImageElement> = {}
const textureLoadingStatusCache: Record<string, boolean> = {}
const textureLoadPromises: Record<string, Promise<HTMLImageElement>> = {} // 添加Promise缓存，避免重复加载

// 标记是否已经开始预加载
let preloadStarted = false

/**
 * 纹理管理Composable
 * 处理纹理加载、缓存和状态管理
 */
export function useTextureManager() {
  // 获取纹理Store
  const textureStore = useTextureStore()

  // 从Store中获取响应式状态
  const { selectedTextureId, textureOpacity, colorAdjustments, shadowSettings } =
    storeToRefs(textureStore)

  // 纹理列表
  const textures = ref<TextureInfo[]>([
    { id: 'amber', name: '琥珀', src: amberTexture },
    { id: 'druzy', name: '晶簇', src: druzyTexture },
    { id: 'emerald', name: '祖母绿', src: emeraldTexture },
    { id: 'fab1', name: '织物1', src: fab1Texture },
    { id: 'fab2', name: '织物2', src: fab2Texture },
    { id: 'granite', name: '花岗岩', src: graniteTexture }
  ])

  // 当前选中的纹理
  const selectedTexture = computed(
    () => textures.value.find((t) => t.id === selectedTextureId.value) || null
  )

  // 创建响应式引用，指向缓存对象
  const textureImages = reactive(textureImagesCache)

  /**
   * 预加载所有纹理
   * @returns 返回一个Promise，当第一个纹理加载完成时解析
   */
  async function preloadTextures(): Promise<string | null> {
    // 避免重复预加载
    if (preloadStarted) {
      return selectedTextureId.value
    }

    preloadStarted = true

    // 如果没有纹理，直接返回null
    if (textures.value.length === 0) {
      return null
    }

    // 获取第一个纹理，确保它存在
    const firstTexture = textures.value[0]
    if (!firstTexture) {
      return null
    }

    // 创建所有纹理的加载Promise数组
    const loadPromises = textures.value.map((texture) => loadTexture(texture.id, texture.src))

    // 在后台加载所有纹理
    Promise.all(loadPromises).catch(() => {
      // 忽略错误，因为我们只关心第一个纹理是否加载成功
    })

    try {
      // 等待第一个纹理加载完成
      await loadTexture(firstTexture.id, firstTexture.src)

      // 只有在没有选中纹理且未初始化过的情况下，才自动选中第一个纹理
      if (selectedTextureId.value === null && !textureStore.hasInitializedTexture) {
        textureStore.setTexture(firstTexture.id)
      }

      return firstTexture.id
    } catch {
      // 加载失败，返回null
      return null
    }
  }

  /**
   * 加载单个纹理
   * @param id 纹理ID
   * @param src 纹理图片路径 (已导入的静态资源)
   * @returns Promise，解析为加载的图片对象
   */
  function loadTexture(id: string, src: string): Promise<HTMLImageElement> {
    // 如果已经加载过，直接返回缓存的图片
    if (textureImagesCache[id]) {
      return Promise.resolve(textureImagesCache[id])
    }

    // 如果已经有一个加载Promise，返回它
    if (textureLoadPromises[id]) {
      return textureLoadPromises[id]
    }

    // 标记为加载中
    textureLoadingStatusCache[id] = true

    // 创建加载Promise
    const loadPromise = new Promise<HTMLImageElement>((resolve, reject) => {
      try {
        // 创建新图片对象
        const img = new Image()

        // 图片加载完成回调
        img.onload = () => {
          // 存储到缓存中，不使用Vue的响应式系统
          textureImagesCache[id] = img
          textureLoadingStatusCache[id] = false

          // 只有在没有选中纹理且未初始化过的情况下，才自动选中加载完成的纹理
          // if (selectedTextureId.value === null && !textureStore.hasInitializedTexture) {
          //   textureStore.setTexture(id)
          // }

          // 解析Promise
          resolve(img)
        }

        // 图片加载失败回调
        img.onerror = (error) => {
          textureLoadingStatusCache[id] = false
          delete textureLoadPromises[id]
          reject(error)
        }

        // 设置图片源 - 对于Vite导入的资源，src已经是处理后的URL
        img.src = src
      } catch (error) {
        textureLoadingStatusCache[id] = false
        delete textureLoadPromises[id]
        reject(error)
      }
    })

    // 存储Promise到缓存
    textureLoadPromises[id] = loadPromise

    return loadPromise
  }

  /**
   * 获取纹理图片对象
   * @param id 纹理ID
   * @returns 纹理图片对象或null
   */
  function getTextureImage(id: string | null): HTMLImageElement | null {
    if (!id) {
      return null
    }
    const image = textureImagesCache[id]
    return image || null
  }

  /**
   * 当前选中的纹理图片
   */
  const selectedTextureImage = computed(() => {
    const image = getTextureImage(selectedTextureId.value)
    return image
  })

  /**
   * 是否正在加载纹理
   */
  const isLoading = computed(() => {
    return Object.values(textureLoadingStatusCache).some((status) => status)
  })

  return {
    // 纹理数据
    textures,
    selectedTexture,
    textureImages,
    selectedTextureImage,
    isLoading,

    // 方法
    preloadTextures,
    loadTexture,
    getTextureImage,

    // Store状态和方法
    selectedTextureId,
    textureOpacity,
    colorAdjustments,
    shadowSettings,

    // Store操作方法
    setTexture: textureStore.setTexture,
    setTextureOpacity: textureStore.setTextureOpacity,
    setHue: textureStore.setHue,
    setSaturation: textureStore.setSaturation,
    setBrightness: textureStore.setBrightness,
    setBaseColor: textureStore.setBaseColor,
    setTextureStrength: textureStore.setTextureStrength,
    setShadowIntensity: textureStore.setShadowIntensity,
    resetTexture: textureStore.resetTexture
  }
}
