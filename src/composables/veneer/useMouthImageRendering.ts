import { ref, computed } from 'vue'
import type { Ref } from 'vue'
import { useCommonStore } from '@/store/common'
import { storeToRefs } from 'pinia'
import { imageToStage as imageToStageUtil } from '@/utils/transform2d'
import type { TransformParams } from '@/utils/transform2d'

/**
 * 处理开口照的渲染参数计算
 */
export function useMouthImageRendering(
  mouthImgNaturalWidth: Ref<number>,
  mouthImgNaturalHeight: Ref<number>,
  imgScale: Ref<number>,
  getTransformParams: () => TransformParams
) {
  // 从store获取数据
  const commonStore = useCommonStore()
  const { faceRotation, mouthToSmileTransform } = storeToRefs(commonStore)

  // 滑块值（0表示完全显示开口照，1表示完全显示微笑照）
  // 默认值为0，表示完全显示开口照
  const sliderValue = ref(0)

  // 开口照透明度，与 sliderValue 反向关联
  // 使用计算属性而不是直接修改值，减少不必要的重新渲染
  const mouthOpacity = computed(() => 1 - sliderValue.value)

  // 计算开口照在画布上的渲染参数
  const mouthRenderParams = computed(() => {
    if (!mouthToSmileTransform.value) return null

    const {
      angle,
      scale,
      offsetX: mouthOffsetX,
      offsetY: mouthOffsetY
    } = mouthToSmileTransform.value

    // 开口照中心点在开口照坐标系中的位置
    const mouthCenterX = mouthImgNaturalWidth.value / 2
    const mouthCenterY = mouthImgNaturalHeight.value / 2

    // 将开口照中心点变换到微笑照坐标系
    // 1. 缩放
    const scaledX = mouthCenterX * scale
    const scaledY = mouthCenterY * scale

    // 2. 旋转
    const cosA = Math.cos(angle)
    const sinA = Math.sin(angle)
    const rotatedX = scaledX * cosA - scaledY * sinA
    const rotatedY = scaledX * sinA + scaledY * cosA

    // 3. 平移
    const mouthCenterInSmile = {
      x: rotatedX + mouthOffsetX,
      y: rotatedY + mouthOffsetY
    }

    // 将微笑照坐标系中的点转换到画布坐标系
    const transformParams = getTransformParams()
    const stagePoint = imageToStageUtil(mouthCenterInSmile, transformParams)

    // 开口照的总旋转角度 = 开口照相对于微笑照的旋转角度 + 微笑照的旋转角度
    const totalAngle = angle + faceRotation.value

    return {
      x: stagePoint.x,
      y: stagePoint.y,
      scale: scale * imgScale.value,
      angle: totalAngle
    }
  })

  return {
    mouthOpacity,
    sliderValue,
    mouthRenderParams
  }
}
