import { ref } from 'vue'
import { useCommonStore } from '@/store/common'
import { storeToRefs } from 'pinia'
import { useCanvasStage } from '@/composables/veneer/useCanvasStage'
import { useImageLoader } from '@/composables/veneer/useImageLoader'
import { useImageTransform } from '@/composables/veneer/useImageTransform'
import { useMouthImageRendering } from '@/composables/veneer/useMouthImageRendering'
import { useImageLoadCoordinator } from '@/composables/veneer/useImageLoadCoordinator'
import { provideVeneerEditorContext, EditMode } from '@/contexts/VeneerEditorContext'

/**
 * 提供贴面编辑器上下文的组合式函数
 * 整合各个子组合式函数，并通过Context API提供给子组件
 */
export function useVeneerEditorProvider() {
  // 从store获取数据
  const commonStore = useCommonStore()
  const { faceRotation, headerHeight } = storeToRefs(commonStore)

  // 1. 画布舞台管理
  const { stageWidth, stageHeight, updateStageSize: updateStageSizeBase } = useCanvasStage()

  // 2. 图片加载管理
  const {
    smileImgObj,
    mouthImgObj,
    smileImgNaturalWidth,
    smileImgNaturalHeight,
    mouthImgNaturalWidth,
    mouthImgNaturalHeight
  } = useImageLoader()

  // 3. 图像变换管理
  const {
    imgScale,
    imgCenter,
    getTransformParams,
    autoFocusToTeethArea,
    handleWheel,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    resetInteraction
  } = useImageTransform(stageWidth, stageHeight, smileImgNaturalWidth, smileImgNaturalHeight)

  // 4. 开口照渲染管理
  const { mouthOpacity, sliderValue, mouthRenderParams } = useMouthImageRendering(
    mouthImgNaturalWidth,
    mouthImgNaturalHeight,
    imgScale,
    getTransformParams
  )

  // 使用图片加载协调器，处理图片加载完成后的自动聚焦
  useImageLoadCoordinator(smileImgObj, autoFocusToTeethArea)

  // 包装updateStageSize，添加自动聚焦功能
  function updateStageSize(container: HTMLElement | null) {
    updateStageSizeBase(container, () => {
      // 当舞台尺寸变化时，自动聚焦到牙齿区域
      autoFocusToTeethArea()
    })
  }

  // 5. 编辑模式管理
  const activeEditMode = ref<EditMode>(EditMode.TRANSFORM)
  const showMidline = ref(true)
  const showSmileFrame = ref(true)
  const mirrorEdit = ref(true)
  const bridgeEdit = ref(true)

  // 设置当前编辑模式
  function setActiveEditMode(mode: EditMode) {
    activeEditMode.value = mode
  }

  // 获取当前编辑模式的标题
  function getEditModeTitle(): string {
    switch (activeEditMode.value) {
      case EditMode.TRANSFORM:
        return '整体结构调整'
      case EditMode.DETAIL:
        return '精细编辑'
      case EditMode.MIRROR:
        return '镜像拷贝'
      default:
        return '整体结构调整'
    }
  }

  // 提供贴面编辑器上下文
  provideVeneerEditorContext({
    // 画布渲染相关状态
    stageWidth,
    stageHeight,
    smileImgObj,
    mouthImgObj,
    smileImgNaturalWidth,
    smileImgNaturalHeight,
    mouthImgNaturalWidth,
    mouthImgNaturalHeight,
    mouthOpacity,
    sliderValue,
    imgScale,
    imgCenter,
    faceRotation,
    mouthRenderParams,
    headerHeight,

    // 编辑模式相关状态
    activeEditMode,
    showMidline,
    showSmileFrame,
    mirrorEdit,
    bridgeEdit,

    // 画布交互方法
    getTransformParams,
    autoFocusToTeethArea,
    handleWheel,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    updateStageSize,
    resetInteraction,

    // 编辑模式方法
    setActiveEditMode,
    getEditModeTitle
  })

  // 返回一些可能在模板中直接使用的值
  return {
    headerHeight,
    stageWidth,
    stageHeight,
    smileImgObj,
    mouthImgObj,
    sliderValue
  }
}
