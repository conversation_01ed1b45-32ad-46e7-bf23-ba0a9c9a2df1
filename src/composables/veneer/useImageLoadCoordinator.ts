import { watch } from 'vue'
import type { Ref } from 'vue'

/**
 * 图片加载与自动聚焦协调器
 * 负责协调图片加载完成后的自动聚焦行为
 * 
 * @param smileImgObj 微笑照片对象
 * @param autoFocusToTeethArea 自动聚焦到牙齿区域的函数
 */
export function useImageLoadCoordinator(
  smileImgObj: Ref<HTMLImageElement | null>,
  autoFocusToTeethArea: () => void
) {
  // 监听图片加载状态
  watch(
    () => smileImgObj.value,
    (newImg) => {
      if (newImg) {
        console.log('微笑照片加载完成，自动聚焦到牙齿区域')
        autoFocusToTeethArea()
      }
    },
    { immediate: true } // 立即检查当前状态
  )
}
