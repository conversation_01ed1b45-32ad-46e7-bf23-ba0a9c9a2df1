import { ref, Ref, computed, watch, ComputedRef } from 'vue'
import type { Point } from '@/utils/curveUtils'
import { findCurveInsertionPoint } from '@/utils/curveUtils'
import type { KonvaEventObject } from 'konva/lib/Node'

/**
 * 处理牙齿形状的 composable
 * 提供牙齿形状管理相关的状态和方法
 *
 * @param segmentInfo 区块信息的响应式引用
 * @param toothId 牙齿ID
 * @param rotationCenter 旋转中心点的响应式引用
 * @param toothCenter 牙齿中心点的计算属性
 * @param getCurrentToothShape 获取当前牙齿形状的函数
 * @param updateToothShape 更新牙齿形状的回调函数
 * @param currentTemplateId 当前模板ID的响应式引用
 * @param customToothShapes 自定义牙齿形状的响应式引用
 * @returns 形状相关的状态和方法
 */
export function useToothShape(
  segmentInfo: Ref<any>,
  toothId: Ref<number>,
  rotationCenter: Ref<Point>,
  toothCenter: ComputedRef<Point>,
  getCurrentToothShape: (id: number) => any,
  updateToothShape: (id: number, shape: any) => void,
  currentTemplateId: Ref<string>,
  customToothShapes: Ref<Record<number, any>>
) {
  // 控制点数量
  const NUM_CONTROL_POINTS = 12

  // 初始化控制点
  const controlPoints = ref<Point[]>([])

  // 标记牙齿形状是否已被自定义
  const isCustomShape = ref(false)

  // 保存原始形状的相对位置（相对于中心点的比例）
  const relativeShape = ref<Point[]>([])

  // 计算牙齿轮廓点
  const outlinePoints = computed(() => {
    if (controlPoints.value.length === 0 && segmentInfo.value) {
      initializeControlPoints()
    }

    // 将控制点转换为扁平数组用于绘制
    return controlPoints.value.flatMap((point) => [point.x, point.y])
  })

  // 监听分割区块信息变化，更新牙齿位置
  watch(
    () => segmentInfo.value,
    () => {
      // 重新初始化控制点
      if (segmentInfo.value) {
        initializeControlPoints()
      }
    },
    { deep: true }
  )

  // 监听当前模板变化，更新牙齿形状
  watch(
    () => currentTemplateId.value,
    () => {
      // 重置自定义形状标记
      isCustomShape.value = false
      // 清空相对形状
      relativeShape.value = []
      // 重新初始化控制点
      if (segmentInfo.value) {
        initializeControlPoints()
      }
    },
    { flush: 'post' } // 确保在DOM更新后执行
  )

  // 监听store中的整个customToothShapes对象变化
  watch(
    () => customToothShapes.value,
    (newShapes) => {
      const newShapeForThisTooth = newShapes[toothId.value]

      let needsReinitialization = false

      if (newShapeForThisTooth && newShapeForThisTooth.points) {
        if (JSON.stringify(relativeShape.value) !== JSON.stringify(newShapeForThisTooth.points)) {
          relativeShape.value = [...newShapeForThisTooth.points]
          needsReinitialization = true
        }
        if (!isCustomShape.value) {
          isCustomShape.value = true
        }
      } else if (isCustomShape.value) {
        isCustomShape.value = false
        relativeShape.value = []
        needsReinitialization = true
      }

      if (needsReinitialization && segmentInfo.value) {
        initializeControlPoints()
      }
    },
    { deep: true }
  )

  /**
   * 初始化控制点
   */
  function initializeControlPoints() {
    if (!segmentInfo.value) return // Guard clause

    const { startX, width, height, topY } = segmentInfo.value
    const centerX = startX + width / 2
    const centerY = topY + height / 2
    const currentToothId = toothId.value

    // 如果已经有自定义形状，则应用相对形状
    if (isCustomShape.value && relativeShape.value.length > 0) {
      // 应用保存的相对形状，根据新的区块尺寸进行缩放
      const points = relativeShape.value.map((point) => ({
        x: centerX + (point.x * width) / 2,
        y: centerY + (point.y * height) / 2
      }))
      controlPoints.value = points
    } else {
      // 从store中获取当前模板的牙齿形状
      const toothShape = getCurrentToothShape(currentToothId)

      if (toothShape && toothShape.points && toothShape.points.length > 0) {
        // 使用模板中的形状
        const points = toothShape.points.map((point: Point) => ({
          x: centerX + (point.x * width) / 2,
          y: centerY + (point.y * height) / 2
        }))
        controlPoints.value = points
      } else {
        // 如果没有模板形状，创建基本的牙齿形状（椭圆形）
        const points = []

        for (let i = 0; i < NUM_CONTROL_POINTS; i++) {
          const angle = (i / NUM_CONTROL_POINTS) * Math.PI * 2
          const x = centerX + Math.cos(angle) * (width / 2) * 0.98 // 稍微缩小一点，不要完全填满区块
          const y = centerY + Math.sin(angle) * (height / 2) * 0.98
          points.push({ x, y })
        }

        controlPoints.value = points
      }
    }
  }

  /**
   * 查找最近的曲线点并计算插入点
   */
  function findInsertionPoint(
    clickPoint: Point,
    points: Point[]
  ): { index: number; point: Point } | null {
    // 使用工具函数，传入参数：点击位置、控制点数组、张力系数、检测阈值、是否闭合
    return findCurveInsertionPoint(clickPoint, points, 0.4, 15, true)
  }

  /**
   * 更新相对形状并保存到store
   */
  function updateLocalAndStoreRelativeShape() {
    if (!segmentInfo.value) return

    // 计算并保存相对形状（相对于中心点的比例）
    const { startX, width, height, topY } = segmentInfo.value
    const centerX = startX + width / 2
    const centerY = topY + height / 2

    // 计算每个点相对于中心点的比例位置
    const newRelativeShape = controlPoints.value.map((point) => ({
      x: (point.x - centerX) / (width / 2),
      y: (point.y - centerY) / (height / 2)
    }))

    // 先更新本地的相对形状
    relativeShape.value = [...newRelativeShape]

    // 标记为自定义形状
    isCustomShape.value = true

    // 更新store中的自定义牙齿形状
    // 使用setTimeout确保在下一个事件循环中更新store，避免触发监听器重新初始化
    setTimeout(() => {
      updateToothShape(toothId.value, {
        points: newRelativeShape // Pass the plain array
      })
    }, 0)
  }

  /**
   * 处理牙齿轮廓点击事件，用于添加控制点
   */
  function handleLineClick(
    e: KonvaEventObject<MouseEvent>,
    isDetailMode: boolean,
    isEdgeDragging: boolean,
    edgeDragJustEnded: boolean
  ) {
    // 如果正在拖拽边缘或刚刚结束拖拽，不处理点击事件
    if (isEdgeDragging || edgeDragJustEnded) {
      e.cancelBubble = true
      return false
    }

    // 在精细编辑模式下才允许添加控制点
    if (isDetailMode) {
      // 获取点击位置
      const stage = e.target.getStage()
      if (!stage) {
        return false
      }
      const pos = stage.getPointerPosition()
      if (!pos) {
        return false
      }

      // 查找最近的曲线点
      const clickPoint = { x: pos.x, y: pos.y }
      const insertionPoint = findInsertionPoint(clickPoint, controlPoints.value)

      if (insertionPoint) {
        // 在找到的位置插入新的控制点
        const newPoints = [...controlPoints.value]
        newPoints.splice(insertionPoint.index, 0, {
          x: insertionPoint.point.x,
          y: insertionPoint.point.y
        })

        // 更新控制点
        controlPoints.value = [...newPoints]

        updateLocalAndStoreRelativeShape()

        return true
      }
    }

    return false
  }

  /**
   * 处理控制点拖拽
   */
  function handlePointDrag(e: KonvaEventObject<DragEvent>, index: number, isDetailMode: boolean) {
    // 只在精细编辑模式下允许拖拽控制点
    if (!isDetailMode) {
      return
    }

    // 直接获取控制点的新位置
    const newX = e.target.x()
    const newY = e.target.y()

    // 更新控制点位置
    const newPoints = [...controlPoints.value]
    newPoints[index] = { x: newX, y: newY }

    controlPoints.value = newPoints

    // 标记为自定义形状并保存到store
    updateLocalAndStoreRelativeShape()

    // 更新旋转中心点，确保旋转中心跟随牙齿形状变化
    if (rotationCenter && toothCenter && typeof toothCenter.value.x === 'number') {
      rotationCenter.value = {
        x: toothCenter.value.x,
        y: toothCenter.value.y
      }
    }
  }

  /**
   * 处理控制点右键点击，用于删除控制点
   */
  function handlePointRightClick(
    e: KonvaEventObject<MouseEvent>,
    index: number,
    isDetailMode: boolean
  ) {
    // 只处理右键点击
    if (e.evt.button !== 2) {
      return false
    }

    // 安全地阻止默认的右键菜单
    try {
      if (e.evt && typeof e.evt.preventDefault === 'function') {
        e.evt.preventDefault()
      }
      if (e.evt && typeof e.evt.stopPropagation === 'function') {
        e.evt.stopPropagation()
      }
    } catch (error) {
      console.warn('Failed to prevent default context menu:', error)
    }
    e.cancelBubble = true

    // 只在精细编辑模式下允许删除控制点
    if (!isDetailMode) {
      return false
    }

    // 至少保留3个控制点，确保能形成一个有效的形状
    if (controlPoints.value.length <= 3) {
      return false
    }

    // 删除指定索引的控制点
    const newPoints = [...controlPoints.value]
    newPoints.splice(index, 1)

    // 更新控制点
    controlPoints.value = newPoints

    // 标记为自定义形状并保存到store
    updateLocalAndStoreRelativeShape()

    // 更新旋转中心点，确保旋转中心跟随牙齿形状变化
    if (rotationCenter && toothCenter && typeof toothCenter.value.x === 'number') {
      rotationCenter.value = {
        x: toothCenter.value.x,
        y: toothCenter.value.y
      }
    }

    return true
  }

  return {
    NUM_CONTROL_POINTS,
    controlPoints,
    isCustomShape,
    relativeShape,
    outlinePoints,
    initializeControlPoints,
    handleLineClick,
    handlePointDrag,
    handlePointRightClick
  }
}
