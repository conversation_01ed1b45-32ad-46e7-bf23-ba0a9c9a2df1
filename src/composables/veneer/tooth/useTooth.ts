import { Ref, computed, ref, toRef, onMounted } from 'vue'
import type { KonvaEventObject } from 'konva/lib/Node'
import { EditMode } from '@/contexts/VeneerEditorContext'
import { useToothHover } from './useToothHover'
import { useToothEdge } from './useToothEdge'
import { useToothTransform } from './useToothTransform'
import { useToothShape } from './useToothShape'
import { useToothInit } from './useToothInit'
import { useMirrorCopy } from './useMirrorCopy'
import type { Point } from '@/utils/curveUtils'
import type { SegmentInfo } from '@/store/teeth'

/**
 * 创建编辑模式计算属性
 */
export function createEditModeComputed(editorContext: any) {
  const isDetailMode = computed<boolean>(
    () => editorContext.activeEditMode.value === EditMode.DETAIL
  )

  const isTransformMode = computed<boolean>(
    () => editorContext.activeEditMode.value === EditMode.TRANSFORM
  )

  return { isDetailMode, isTransformMode }
}

/**
 * 计算镜像牙齿ID
 */
export function getMirrorToothId(id: number) {
  // 左右牙齿ID是相反数
  return id !== 0 ? -id : null
}

/**
 * 整合所有牙齿相关功能的 composable
 * 提供牙齿的所有功能和事件处理
 *
 * @param lineRef 牙齿轮廓线的引用
 * @param props 组件属性
 * @param editorContext 贴面编辑器上下文
 * @param teethStore 牙齿状态存储
 * @returns 牙齿相关的所有功能和事件处理
 */
export function useTooth(
  lineRef: Ref<any>,
  props: {
    segmentInfo: SegmentInfo
    selected: boolean
    listening: boolean
    toothId: number
  },
  editorContext: any,
  teethStore: any
) {
  // 创建编辑模式计算属性
  const { isDetailMode, isTransformMode } = createEditModeComputed(editorContext)

  // 初始化一些变量，稍后会被 toothShape 中的值覆盖
  const controlPoints = ref<Point[]>([])
  const isCustomShape = ref(false)
  const relativeShape = ref<Point[]>([])

  // 使用牙齿悬停效果 composable
  const { isHovered, handleClick, getStrokeColor } = useToothHover(toRef(props, 'selected'))

  // 使用牙齿变换 composable
  const {
    rotateIconObj,
    rotateIconScale,
    toothCenter,
    rotateHandlePosition,
    rotationCenter,
    isRotating,
    currentRotateHandlePosition,
    handleRotateMouseEnter,
    handleRotateMouseLeave,
    handleRotateStart,
    handleRotate,
    handleRotateEnd,
    handleDragStart,
    handleDrag,
    handleDragEnd
  } = useToothTransform(
    controlPoints,
    toRef(props, 'segmentInfo'),
    isTransformMode,
    isCustomShape,
    relativeShape,
    toRef(props, 'toothId'),
    editorContext.mirrorEdit,
    teethStore.updateToothShape,
    getMirrorToothId,
    (id) => teethStore.segments.find((segment: { index: number }) => segment.index === id),
    teethStore.getCurrentToothShape
  )

  // 使用牙齿边缘拖拽 composable
  const {
    isEdgeDragging,
    edgeDragJustEnded,
    isBridgeDragging,
    bridgeAdjacentToothId,
    isBridgeHovered,
    handleMouseMove: handleEdgeMouseMove,
    handleMouseDown: handleEdgeMouseDown
  } = useToothEdge(
    controlPoints,
    toRef(props, 'segmentInfo'),
    isTransformMode,
    isRotating,
    toRef(props, 'toothId'),
    editorContext.mirrorEdit,
    editorContext.bridgeEdit,
    teethStore.updateToothShape,
    teethStore.getCurrentToothShape,
    getMirrorToothId,
    (id: number) => teethStore.segments.find((segment: { index: number }) => segment.index === id)
  )

  // 使用牙齿形状管理 composable
  const toothShape = useToothShape(
    toRef(props, 'segmentInfo'),
    toRef(props, 'toothId'),
    rotationCenter,
    toothCenter,
    teethStore.getCurrentToothShape,
    teethStore.updateToothShape,
    toRef(teethStore, 'currentTemplateId'),
    toRef(teethStore, 'customToothShapes')
  )

  // 使用牙齿初始化 composable
  const { setupShapeWatchers, initializeTooth } = useToothInit(
    controlPoints,
    isCustomShape,
    relativeShape,
    isRotating,
    edgeDragJustEnded,
    toRef(props, 'selected'),
    isHovered,
    rotateIconObj,
    toothShape.initializeControlPoints
  )

  // 使用镜像拷贝 composable
  const {
    isMirrorMode,
    showMirrorPreview,
    targetToothId,
    handleMouseEnter: handleMirrorMouseEnter,
    handleMouseLeave: handleMirrorMouseLeave,
    handleClick: handleMirrorClick
  } = useMirrorCopy(editorContext, teethStore, toRef(props, 'toothId'), lineRef)

  // 设置形状监听器
  setupShapeWatchers(toothShape)

  // 计算牙齿轮廓点
  const outlinePoints = computed(() => {
    return toothShape.outlinePoints.value
  })

  // ===== 事件处理包装函数 =====

  /**
   * 处理牙齿轮廓点击事件的包装函数
   */
  function handleLineClick(e: KonvaEventObject<MouseEvent>) {
    // 如果正在拖拽边缘或刚刚结束拖拽，不处理点击事件
    if (isEdgeDragging.value || edgeDragJustEnded.value) {
      e.cancelBubble = true
      return
    }

    // 如果处于镜像拷贝模式，执行镜像拷贝
    if (isMirrorMode.value) {
      // 使用镜像拷贝 composable 处理点击事件
      const handled = handleMirrorClick()
      if (handled) {
        e.cancelBubble = true
        return true
      }
    }

    // 如果牙齿未被选中，则选中牙齿
    if (!props.selected) {
      // 使用 composable 处理点击事件
      handleClick()
      // 选中当前牙齿
      teethStore.selectTooth(props.toothId)
      return
    }

    // 阻止事件冒泡
    e.cancelBubble = true

    // 使用 toothShape 中的方法处理点击事件
    const shapeHandled = toothShape.handleLineClick(
      e,
      isDetailMode.value,
      isEdgeDragging.value,
      edgeDragJustEnded.value
    )

    return shapeHandled
  }

  /**
   * 处理拖拽开始的包装函数
   */
  function handleDragStartWrapper() {
    // 只在变换模式下允许拖动
    if (!isTransformMode.value) {
      return
    }

    // 调用 composable 中的拖拽开始方法
    handleDragStart()
  }

  /**
   * 处理拖拽的包装函数
   */
  function handleDragWrapper(e: KonvaEventObject<DragEvent>) {
    // 只在变换模式下允许拖动
    if (!isTransformMode.value) {
      return
    }

    // 获取拖拽的偏移量
    const node = e.target
    const deltaX = node.x()
    const deltaY = node.y()

    // 调用 composable 中的拖拽方法
    handleDrag(deltaX, deltaY)

    // 重置节点位置，因为我们直接更新控制点
    node.x(0)
    node.y(0)
  }

  /**
   * 处理拖拽结束的包装函数
   */
  function handleDragEndWrapper() {
    // 只在变换模式下允许拖动
    if (!isTransformMode.value) {
      return
    }

    // 确保线条位置为0,0
    if (lineRef.value) {
      const line = lineRef.value.getNode()
      if (line) {
        line.x(0)
        line.y(0)
      }
    }

    // 调用 composable 中的拖拽结束方法
    handleDragEnd()
  }

  /**
   * 处理旋转开始的包装函数
   */
  function handleRotateStartWrapper(e: KonvaEventObject<DragEvent>) {
    // 获取舞台引用
    const stage = e.target.getStage()
    if (!stage) return

    // 调用 composable 中的旋转开始方法
    handleRotateStart(e, stage)
  }

  /**
   * 处理旋转的包装函数
   */
  function handleRotateWrapper(e: KonvaEventObject<DragEvent>) {
    // 获取舞台引用
    const stage = e.target.getStage()
    if (!stage) return

    // 调用 composable 中的旋转方法
    handleRotate(e, stage)
  }

  /**
   * 处理鼠标在牙齿轮廓上移动的包装函数
   */
  function handleLineMouseMove(e: KonvaEventObject<MouseEvent>) {
    // 如果处于镜像拷贝模式，显示镜像预览
    if (isMirrorMode.value) {
      console.log('牙齿轮廓鼠标移动，处于镜像模式，牙齿ID:', props.toothId)
      // 设置鼠标样式为指针
      document.body.style.cursor = 'pointer'
      // 显示镜像预览
      handleMirrorMouseEnter()
      // 阻止事件冒泡
      e.cancelBubble = true
      return
    }

    // 使用牙齿悬停效果 composable
    if (!props.selected) {
      isHovered.value = true
    }

    // 使用牙齿边缘拖拽 composable
    handleEdgeMouseMove(e)
  }

  /**
   * 处理鼠标按下事件的包装函数
   */
  function handleLineMouseDown(e: KonvaEventObject<MouseEvent>) {
    // 如果处于镜像拷贝模式，不处理鼠标按下事件
    if (isMirrorMode.value) {
      return
    }

    // 使用牙齿边缘拖拽 composable
    const handled = handleEdgeMouseDown(e)

    // 如果边缘拖拽处理了事件，则返回
    if (handled) {
      return
    }

    // 其他鼠标按下事件处理逻辑...
  }

  /**
   * 处理鼠标离开牙齿轮廓的包装函数
   */
  function handleLineMouseLeave() {
    // 如果处于镜像拷贝模式，隐藏镜像预览
    if (isMirrorMode.value) {
      console.log('牙齿轮廓鼠标离开，处于镜像模式，牙齿ID:', props.toothId)
      // 恢复默认鼠标样式
      document.body.style.cursor = 'default'
      // 隐藏镜像预览
      handleMirrorMouseLeave()
      return
    }

    // 使用牙齿悬停效果 composable
    isHovered.value = false

    // 如果不在拖拽状态，恢复默认鼠标样式
    if (!isEdgeDragging.value) {
      document.body.style.cursor = 'default'
    }
  }

  /**
   * 处理控制点拖拽的包装函数
   */
  function handlePointDragWrapper(e: KonvaEventObject<DragEvent>, index: number) {
    // 使用传入的方法处理控制点拖拽
    toothShape.handlePointDrag(e, index, isDetailMode.value)
  }

  // 控制点 hover 状态
  const hoveredControlPointIndex = ref<number | null>(null)

  /**
   * 处理控制点鼠标进入事件
   */
  function handlePointMouseEnter(index: number) {
    hoveredControlPointIndex.value = index
    document.body.style.cursor = 'pointer'
  }

  /**
   * 处理控制点鼠标离开事件
   */
  function handlePointMouseLeave() {
    hoveredControlPointIndex.value = null
    document.body.style.cursor = 'default'
  }

  /**
   * 处理控制点鼠标按下事件的综合包装函数
   * 支持左键拖拽和右键删除
   */
  function handlePointMouseDownWrapper(e: KonvaEventObject<MouseEvent>, index: number) {
    // 如果是右键，尝试删除控制点
    if (e.evt.button === 2) {
      const handled = toothShape.handlePointRightClick(e, index, isDetailMode.value)
      return handled
    }

    // 左键和中键不在这里处理，交给拖拽系统
    return false
  }

  // 在组件挂载时初始化牙齿
  onMounted(initializeTooth)

  return {
    // 状态
    isDetailMode,
    isTransformMode,
    isHovered,
    isEdgeDragging,
    edgeDragJustEnded,
    isRotating,
    isCustomShape,

    // 镜像拷贝相关
    isMirrorMode,
    showMirrorPreview,
    targetToothId,

    // 连桥编辑相关
    isBridgeDragging,
    bridgeAdjacentToothId,
    isBridgeHovered,

    // 形状相关
    controlPoints,
    relativeShape,
    outlinePoints,
    toothCenter,
    rotationCenter,

    // 旋转相关
    rotateIconObj,
    rotateIconScale,
    rotateHandlePosition,
    currentRotateHandlePosition,

    // 控制点 hover 相关
    hoveredControlPointIndex,

    // 事件处理函数
    getStrokeColor,
    handleLineClick,
    handleDragStartWrapper,
    handleDragWrapper,
    handleDragEndWrapper,
    handleRotateStartWrapper,
    handleRotateWrapper,
    handleRotateEnd,
    handleLineMouseMove,
    handleLineMouseDown,
    handleLineMouseLeave,
    handlePointDragWrapper,
    handlePointMouseEnter,
    handlePointMouseLeave,
    handlePointMouseDownWrapper,
    handleRotateMouseEnter,
    handleRotateMouseLeave
  }
}
