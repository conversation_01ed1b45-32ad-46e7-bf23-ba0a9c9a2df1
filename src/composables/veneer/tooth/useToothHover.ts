import { ref, Ref, watch } from 'vue'

/**
 * 处理牙齿悬停效果的 composable
 * 提供牙齿悬停状态管理和相关事件处理
 *
 * @param selected 牙齿是否被选中的响应式引用
 * @returns 悬停状态和处理函数
 */
export function useToothHover(selected: Ref<boolean>) {
  // 鼠标悬停状态
  const isHovered = ref(false)

  // 监听选中状态变化
  watch(selected, (newSelected) => {
    // 如果牙齿被选中，重置悬停状态
    if (newSelected) {
      isHovered.value = false
    }
  })

  /**
   * 处理鼠标进入牙齿区域
   * 如果牙齿未被选中，设置悬停状态为 true
   */
  function handleMouseEnter() {
    if (!selected.value) {
      isHovered.value = true
    }
  }

  /**
   * 处理鼠标离开牙齿区域
   * 重置悬停状态为 false
   */
  function handleMouseLeave() {
    isHovered.value = false
  }

  /**
   * 处理牙齿点击事件
   * 重置悬停状态
   */
  function handleClick() {
    isHovered.value = false
  }

  /**
   * 处理鼠标移动事件
   * 在 Konva 中使用，结合现有的鼠标移动处理逻辑
   * @param e Konva 鼠标事件对象
   */
  function handleMouseMove() {
    // 设置悬停状态（如果牙齿未被选中）
    if (!selected.value) {
      isHovered.value = true
    }
  }

  /**
   * 获取悬停状态下的描边颜色
   * @param defaultColor 默认颜色
   * @param hoverColor 悬停颜色
   * @param selectedColor 选中颜色
   * @returns 当前状态下应使用的颜色
   */
  function getStrokeColor(
    defaultColor: string = '#ffffff',
    hoverColor: string = '#ff9f9f',
    selectedColor: string = '#ff6b6b'
  ): string {
    if (selected.value) {
      return selectedColor
    } else if (isHovered.value) {
      return hoverColor
    } else {
      return defaultColor
    }
  }

  return {
    isHovered,
    handleMouseEnter,
    handleMouseLeave,
    handleClick,
    handleMouseMove,
    getStrokeColor
  }
}
