import { ref, Ref, computed, ComputedRef, onMounted } from 'vue'
import type { KonvaEventObject } from 'konva/lib/Node'
import type { Point } from '@/utils/curveUtils'

/**
 * 处理牙齿变换（旋转、拖拽）的 composable
 * 提供牙齿旋转和拖拽相关的状态和方法
 *
 * @param controlPoints 牙齿控制点的响应式引用
 * @param segmentInfo 区块信息的响应式引用
 * @param isTransformMode 是否处于变换模式的计算属性
 * @param isCustomShape 是否为自定义形状的响应式引用
 * @param relativeShape 相对形状的响应式引用
 * @param toothId 牙齿ID的响应式引用
 * @param mirrorEdit 是否启用镜像编辑的响应式引用
 * @param updateToothShape 更新牙齿形状的回调函数
 * @param getMirrorToothId 获取镜像牙齿ID的函数
 * @param getSegmentById 根据ID获取区块信息的函数
 * @param getCurrentToothShape 获取当前牙齿形状的函数
 * @returns 变换相关的状态和方法
 */
export function useToothTransform(
  controlPoints: Ref<Point[]>,
  segmentInfo: Ref<any>,
  isTransformMode: ComputedRef<boolean>,
  isCustomShape: Ref<boolean>,
  relativeShape: Ref<Point[]>,
  toothId: Ref<number>,
  mirrorEdit: Ref<boolean>,
  updateToothShape: (id: number, shape: any) => void,
  getMirrorToothId: (id: number) => number | null,
  getSegmentById: (id: number) => any | undefined,
  getCurrentToothShape: (id: number) => any
) {
  // 旋转图标对象
  const rotateIconObj = ref<HTMLImageElement | null>(null)
  const rotateIconActive = ref<boolean>(false)
  const rotateIconScale = ref<number>(1)

  // 计算牙齿的实际中心点
  const toothCenter = computed(() => {
    if (controlPoints.value.length === 0) {
      // 如果没有控制点，使用区块信息计算默认中心点
      const { startX, width, height, topY } = segmentInfo.value
      return {
        x: startX + width / 2,
        y: topY + height / 2
      }
    }

    // 使用控制点的平均位置计算牙齿的实际中心点
    const sumX = controlPoints.value.reduce((sum, point) => sum + point.x, 0)
    const sumY = controlPoints.value.reduce((sum, point) => sum + point.y, 0)
    return {
      x: sumX / controlPoints.value.length,
      y: sumY / controlPoints.value.length
    }
  })

  // 计算旋转控制手柄的位置
  const rotateHandlePosition = computed(() => {
    if (controlPoints.value.length === 0) {
      return { x: 0, y: 0 }
    }

    // 使用牙齿的实际中心点
    const centerX = toothCenter.value.x
    const centerY = toothCenter.value.y

    // 计算牙齿的近似高度（使用控制点的最大Y和最小Y之差）
    const minY = Math.min(...controlPoints.value.map((p) => p.y))
    const maxY = Math.max(...controlPoints.value.map((p) => p.y))
    const height = maxY - minY

    // 将旋转控制手柄放在牙齿下方
    return {
      x: centerX,
      y: centerY + height / 2 + 25 // 放在牙齿下方25像素处
    }
  })

  // 旋转相关变量
  const rotationCenter = ref<Point>({ x: 0, y: 0 })
  const initialAngle = ref<number>(0)
  const rotateHandleRadius = ref<number>(0) // 旋转手柄到中心点的距离
  const isRotating = ref<boolean>(false) // 是否正在旋转
  const currentRotateHandlePosition = ref<Point>({ x: 0, y: 0 }) // 当前旋转手柄位置

  // 拖动状态变量
  const initialDragPoints = ref<Point[]>([])
  let dragUpdateScheduled = false

  // 加载旋转图标
  onMounted(() => {
    const img = new Image()
    img.src = new URL('../../../assets/images/rotate.svg', import.meta.url).href
    img.onload = () => {
      rotateIconObj.value = img
    }
  })

  /**
   * 处理旋转图标鼠标悬停
   */
  function handleRotateMouseEnter() {
    document.body.style.cursor = 'ew-resize'
    rotateIconActive.value = true
    rotateIconScale.value = 1.1 // 放大图标
  }

  /**
   * 处理旋转图标鼠标离开
   */
  function handleRotateMouseLeave() {
    document.body.style.cursor = 'default'
    rotateIconActive.value = false
    rotateIconScale.value = 1 // 恢复图标大小
  }

  /**
   * 处理旋转开始
   */
  function handleRotateStart(_e: KonvaEventObject<any>, stage: any) {
    // 只在变换模式下允许旋转
    if (!isTransformMode.value) {
      return
    }

    // 设置鼠标样式
    document.body.style.cursor = 'ew-resize'

    // 标记为正在旋转状态
    isRotating.value = true

    // 使用牙齿的实际中心点作为旋转中心
    rotationCenter.value = {
      x: toothCenter.value.x,
      y: toothCenter.value.y
    }

    // 初始化当前旋转手柄位置
    // 如果已经有值（比如从上一次旋转或拖拽中保留的），则使用当前值
    // 否则使用计算属性中的值
    if (!currentRotateHandlePosition.value.x && !currentRotateHandlePosition.value.y) {
      currentRotateHandlePosition.value = {
        x: rotateHandlePosition.value.x,
        y: rotateHandlePosition.value.y
      }
    }

    // 记录初始角度
    if (!stage) return
    const pos = stage.getPointerPosition()
    if (!pos) return

    // 计算鼠标位置相对于中心点的角度
    initialAngle.value = Math.atan2(pos.y - rotationCenter.value.y, pos.x - rotationCenter.value.x)

    // 计算旋转手柄到中心点的距离
    rotateHandleRadius.value = Math.sqrt(
      Math.pow(currentRotateHandlePosition.value.x - rotationCenter.value.x, 2) +
        Math.pow(currentRotateHandlePosition.value.y - rotationCenter.value.y, 2)
    )
  }

  /**
   * 处理旋转
   */
  function handleRotate(_e: KonvaEventObject<any>, stage: any) {
    // 只在变换模式下允许旋转
    if (!isTransformMode.value || !isRotating.value) {
      return
    }

    if (!stage) return
    const pos = stage.getPointerPosition()
    if (!pos) return

    // 计算当前角度
    const currentAngle = Math.atan2(pos.y - rotationCenter.value.y, pos.x - rotationCenter.value.x)

    // 计算角度差
    const angleDiff = currentAngle - initialAngle.value

    // 应用旋转到所有控制点
    if (controlPoints.value.length > 0) {
      const newPoints = controlPoints.value.map((point) => {
        // 将点坐标转换为相对于旋转中心的坐标
        const relX = point.x - rotationCenter.value.x
        const relY = point.y - rotationCenter.value.y

        // 应用旋转变换
        const cos = Math.cos(angleDiff)
        const sin = Math.sin(angleDiff)
        const newRelX = relX * cos - relY * sin
        const newRelY = relX * sin + relY * cos

        // 转换回绝对坐标
        return {
          x: newRelX + rotationCenter.value.x,
          y: newRelY + rotationCenter.value.y
        }
      })

      // 更新控制点
      controlPoints.value = newPoints

      // 标记为自定义形状
      isCustomShape.value = true

      // 计算并保存相对形状（相对于中心点的比例）
      updateRelativeShape(newPoints)

      // 镜像编辑：如果启用了镜像编辑，同步更新对应的镜像牙齿
      if (mirrorEdit.value) {
        const mirrorId = getMirrorToothId(toothId.value)
        if (mirrorId !== null) {
          // 获取镜像牙齿的区块信息
          const mirrorSegment = getSegmentById(mirrorId)

          if (mirrorSegment) {
            // 获取镜像牙齿当前的形状
            const mirrorShape = getCurrentToothShape(mirrorId)

            if (mirrorShape && mirrorShape.points && mirrorShape.points.length > 0) {
              // 对于镜像牙齿，我们需要应用相同的旋转，但方向相反

              // 计算镜像牙齿的实际中心点（而不是使用区块中心点）
              // 首先将相对坐标转换为绝对坐标
              const mirrorAbsPoints = mirrorShape.points.map((point: Point) => {
                const mirrorSegmentCenterX = mirrorSegment.startX + mirrorSegment.width / 2
                const mirrorSegmentCenterY = mirrorSegment.topY + mirrorSegment.height / 2
                return {
                  x: mirrorSegmentCenterX + point.x * (mirrorSegment.width / 2),
                  y: mirrorSegmentCenterY + point.y * (mirrorSegment.height / 2)
                }
              })

              // 使用绝对坐标计算镜像牙齿的实际中心点
              const mirrorSumX = mirrorAbsPoints.reduce(
                (sum: number, point: Point) => sum + point.x,
                0
              )
              const mirrorSumY = mirrorAbsPoints.reduce(
                (sum: number, point: Point) => sum + point.y,
                0
              )
              const mirrorCenterX = mirrorSumX / mirrorAbsPoints.length
              const mirrorCenterY = mirrorSumY / mirrorAbsPoints.length

              // 使用实际中心点进行旋转变换
              const mirrorPoints = mirrorShape.points.map((point: Point) => {
                // 将相对坐标转换为绝对坐标
                const mirrorSegmentCenterX = mirrorSegment.startX + mirrorSegment.width / 2
                const mirrorSegmentCenterY = mirrorSegment.topY + mirrorSegment.height / 2
                const absX = mirrorSegmentCenterX + point.x * (mirrorSegment.width / 2)
                const absY = mirrorSegmentCenterY + point.y * (mirrorSegment.height / 2)

                // 将点坐标转换为相对于实际中心点的坐标
                const relX = absX - mirrorCenterX
                const relY = absY - mirrorCenterY

                // 应用旋转变换（注意角度是相反的）
                const cos = Math.cos(-angleDiff)
                const sin = Math.sin(-angleDiff)
                const newRelX = relX * cos - relY * sin
                const newRelY = relX * sin + relY * cos

                // 转换回相对坐标（相对于区块中心）
                return {
                  x: (newRelX + mirrorCenterX - mirrorSegmentCenterX) / (mirrorSegment.width / 2),
                  y: (newRelY + mirrorCenterY - mirrorSegmentCenterY) / (mirrorSegment.height / 2)
                }
              })

              // 更新镜像牙齿的形状
              updateToothShape(mirrorId, {
                points: mirrorPoints
              })
            }
          }
        }
      }

      // 更新旋转手柄位置，使其保持与中心点的固定距离和角度
      const handleAngle =
        Math.atan2(
          currentRotateHandlePosition.value.y - rotationCenter.value.y,
          currentRotateHandlePosition.value.x - rotationCenter.value.x
        ) + angleDiff

      // 使用旋转手柄到中心点的固定距离和新角度计算新位置
      currentRotateHandlePosition.value = {
        x: rotationCenter.value.x + rotateHandleRadius.value * Math.cos(handleAngle),
        y: rotationCenter.value.y + rotateHandleRadius.value * Math.sin(handleAngle)
      }

      // 更新初始角度，以便下一次旋转基于当前角度
      initialAngle.value = currentAngle
    }
  }

  /**
   * 处理旋转结束
   */
  function handleRotateEnd() {
    // 只在变换模式下允许旋转
    if (!isTransformMode.value) {
      return
    }

    // 标记旋转结束
    isRotating.value = false

    // 恢复鼠标样式
    document.body.style.cursor = 'default'

    // 更新旋转中心点，确保旋转中心与牙齿中心一致
    rotationCenter.value = {
      x: toothCenter.value.x,
      y: toothCenter.value.y
    }

    // 更新旋转手柄的位置，使其与rotateHandlePosition保持一致
    // 这样在下一次旋转开始时，手柄位置会正确显示
    if (rotateHandleRadius.value > 0) {
      currentRotateHandlePosition.value = {
        x: rotateHandlePosition.value.x,
        y: rotateHandlePosition.value.y
      }
    }
  }

  /**
   * 处理拖拽开始
   */
  function handleDragStart() {
    // 只在变换模式下允许拖动
    if (!isTransformMode.value) {
      return
    }

    // 记录拖拽开始时的控制点
    initialDragPoints.value = [...controlPoints.value]
  }

  /**
   * 处理整体拖拽
   */
  function handleDrag(deltaX: number, deltaY: number) {
    // 只在变换模式下允许拖动
    if (!isTransformMode.value) {
      return
    }

    // 如果有偏移量，则更新所有控制点位置
    if (deltaX !== 0 || deltaY !== 0) {
      // 使用requestAnimationFrame优化性能
      if (!dragUpdateScheduled) {
        dragUpdateScheduled = true
        requestAnimationFrame(() => {
          // 基于初始点和偏移量计算新的控制点位置
          const newPoints = initialDragPoints.value.map((point) => ({
            x: point.x + deltaX,
            y: point.y + deltaY
          }))

          // 更新控制点
          controlPoints.value = newPoints

          // 标记为自定义形状
          isCustomShape.value = true

          // 计算并保存相对形状（相对于中心点的比例）
          updateRelativeShape(newPoints)

          // 更新旋转中心点，确保旋转中心跟随牙齿移动
          rotationCenter.value = {
            x: toothCenter.value.x,
            y: toothCenter.value.y
          }

          // 如果旋转手柄位置已经初始化，也需要更新它的位置
          if (rotateHandleRadius.value > 0) {
            // 保持旋转手柄与中心点的相对位置关系
            // 使用当前旋转手柄位置计算角度，以保持一致性
            const handleAngle = Math.atan2(
              currentRotateHandlePosition.value.y - rotationCenter.value.y,
              currentRotateHandlePosition.value.x - rotationCenter.value.x
            )

            // 使用旋转手柄到中心点的固定距离和角度计算新位置
            currentRotateHandlePosition.value = {
              x: rotationCenter.value.x + rotateHandleRadius.value * Math.cos(handleAngle),
              y: rotationCenter.value.y + rotateHandleRadius.value * Math.sin(handleAngle)
            }
          }

          dragUpdateScheduled = false
        })
      }
    }
  }

  /**
   * 处理拖拽结束
   */
  function handleDragEnd() {
    // 只在变换模式下允许拖动
    if (!isTransformMode.value) {
      return
    }

    // 拖动结束后更新旋转中心点，确保旋转中心跟随牙齿移动
    rotationCenter.value = {
      x: toothCenter.value.x,
      y: toothCenter.value.y
    }

    // 如果旋转手柄位置已经初始化，也需要更新它的位置
    if (rotateHandleRadius.value > 0) {
      // 保持旋转手柄与中心点的相对位置关系
      // 使用当前旋转手柄位置计算角度，以保持一致性
      const handleAngle = Math.atan2(
        currentRotateHandlePosition.value.y - rotationCenter.value.y,
        currentRotateHandlePosition.value.x - rotationCenter.value.x
      )

      // 使用旋转手柄到中心点的固定距离和角度计算新位置
      currentRotateHandlePosition.value = {
        x: rotationCenter.value.x + rotateHandleRadius.value * Math.cos(handleAngle),
        y: rotationCenter.value.y + rotateHandleRadius.value * Math.sin(handleAngle)
      }
    }
  }

  /**
   * 更新相对形状并保存到store
   */
  function updateRelativeShape(points: Point[]) {
    // 计算并保存相对形状（相对于中心点的比例）
    const { startX, width, height, topY } = segmentInfo.value
    const centerX = startX + width / 2
    const centerY = topY + height / 2

    // 计算每个点相对于中心点的比例位置
    const newRelativeShape = points.map((point) => ({
      x: (point.x - centerX) / (width / 2),
      y: (point.y - centerY) / (height / 2)
    }))

    relativeShape.value = newRelativeShape

    // 更新store中的自定义牙齿形状
    updateToothShape(toothId.value, {
      points: newRelativeShape
    })
  }

  return {
    rotateIconObj,
    rotateIconActive,
    rotateIconScale,
    toothCenter,
    rotateHandlePosition,
    rotationCenter,
    isRotating,
    currentRotateHandlePosition,
    initialDragPoints,
    handleRotateMouseEnter,
    handleRotateMouseLeave,
    handleRotateStart,
    handleRotate,
    handleRotateEnd,
    handleDragStart,
    handleDrag,
    handleDragEnd
  }
}
