import { ref, onMounted } from 'vue'

/**
 * 框架图标管理的组合式函数
 * 处理框架控制图标的加载和管理
 */
export function useFrameIcons(moveIcon: string, moveHIcon: string, moveVIcon: string) {
  // 加载SVG图像
  const moveIconObj = ref<HTMLImageElement | null>(null)
  const moveHIconObj = ref<HTMLImageElement | null>(null)
  const moveVIconObj = ref<HTMLImageElement | null>(null)

  // 加载图标
  onMounted(() => {
    // 加载整体移动SVG图像
    const imgMove = new Image()
    imgMove.src = moveIcon
    imgMove.onload = () => {
      moveIconObj.value = imgMove
    }

    // 加载水平移动SVG图像
    const imgH = new Image()
    imgH.src = moveHIcon
    imgH.onload = () => {
      moveHIconObj.value = imgH
    }

    // 加载垂直移动SVG图像
    const imgV = new Image()
    imgV.src = moveVIcon
    imgV.onload = () => {
      moveVIconObj.value = imgV
    }
  })

  return {
    moveIconObj,
    moveHIconObj,
    moveVIconObj
  }
}
