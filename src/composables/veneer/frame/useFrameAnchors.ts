import { Ref, ComputedRef } from 'vue'
import { useTeethStore, TeethFrame } from '@/store/teeth'
import type { KonvaEventObject } from 'konva/lib/Node'
import Konva from 'konva'

// 最小垂直间距
export const MIN_VERTICAL_SPACING = 50

// 最小水平间距
export const MIN_HORIZONTAL_SPACING = 300

/**
 * 创建曲线端点拖动边界函数
 * @param isTopCurve 是否为上曲线
 * @param fixedX 固定的X坐标
 * @param topCurveEndpointY 上曲线端点Y坐标
 * @param bottomCurveEndpointY 下曲线端点Y坐标
 * @returns 拖动边界函数
 */
export function createCurveEndpointDragBoundFunc(
  isTopCurve: boolean,
  fixedX: ComputedRef<number>,
  topCurveEndpointY: ComputedRef<number>,
  bottomCurveEndpointY: ComputedRef<number>
) {
  return function (pos: any) {
    // 只允许垂直方向拖动，X坐标固定
    let newY = pos.y

    if (isTopCurve) {
      // 上曲线端点拖动限制
      // 确保上曲线端点不会低于下曲线端点
      const minY = bottomCurveEndpointY.value - MIN_VERTICAL_SPACING
      if (newY > minY) {
        newY = minY
      }
    } else {
      // 下曲线端点拖动限制
      // 确保下曲线端点不会高于上曲线端点
      const maxY = topCurveEndpointY.value + MIN_VERTICAL_SPACING
      if (newY < maxY) {
        newY = maxY
      }
    }

    return { x: fixedX.value, y: newY }
  }
}

/**
 * 创建曲线中点拖动边界函数
 * @param isTopCurve 是否为上曲线
 * @param frame 框架数据
 * @param currentMidX 当前中点X坐标
 * @param topCurveMidY 上曲线中点Y坐标
 * @param bottomCurveMidY 下曲线中点Y坐标
 * @returns 拖动边界函数
 */
export function createCurveMidDragBoundFunc(
  isTopCurve: boolean,
  frame: ComputedRef<TeethFrame>,
  currentMidX: ComputedRef<number>,
  topCurveMidY: ComputedRef<number>,
  bottomCurveMidY: ComputedRef<number>
) {
  return function (pos: any) {
    // 只允许垂直方向拖动，X坐标固定在中心
    let newY = pos.y

    if (isTopCurve) {
      // 上曲线中点拖动限制

      // 1. 确保上曲线中点不会低于下曲线中点
      const maxY = bottomCurveMidY.value - MIN_VERTICAL_SPACING
      if (newY > maxY) {
        newY = maxY
      }

      // 2. 确保端点之间的距离不会小于最小间距
      // 获取上曲线端点位置
      const topLeftY = frame.value.topCurvePoints.leftY + frame.value.y
      const topRightY = frame.value.topCurvePoints.rightY + frame.value.y

      // 获取下曲线端点位置
      const bottomLeftY = frame.value.bottomCurvePoints.leftY + frame.value.y
      const bottomRightY = frame.value.bottomCurvePoints.rightY + frame.value.y

      // 计算当前中点与端点的差值
      const currentDiffLeft = topCurveMidY.value - topLeftY
      const currentDiffRight = topCurveMidY.value - topRightY

      // 计算新的端点位置
      const newLeftY = newY - currentDiffLeft
      const newRightY = newY - currentDiffRight

      // 检查是否会超出限制
      if (
        newLeftY > bottomLeftY - MIN_VERTICAL_SPACING ||
        newRightY > bottomRightY - MIN_VERTICAL_SPACING
      ) {
        // 计算允许的最大Y值
        const maxYLeft = bottomLeftY - MIN_VERTICAL_SPACING + currentDiffLeft
        const maxYRight = bottomRightY - MIN_VERTICAL_SPACING + currentDiffRight
        newY = Math.min(maxYLeft, maxYRight)
      }
    } else {
      // 下曲线中点拖动限制

      // 1. 确保下曲线中点不会高于上曲线中点
      const minY = topCurveMidY.value + MIN_VERTICAL_SPACING
      if (newY < minY) {
        newY = minY
      }

      // 2. 确保端点之间的距离不会小于最小间距
      // 获取上曲线端点位置
      const topLeftY = frame.value.topCurvePoints.leftY + frame.value.y
      const topRightY = frame.value.topCurvePoints.rightY + frame.value.y

      // 获取下曲线端点位置
      const bottomLeftY = frame.value.bottomCurvePoints.leftY + frame.value.y
      const bottomRightY = frame.value.bottomCurvePoints.rightY + frame.value.y

      // 计算当前中点与端点的差值
      const currentDiffLeft = bottomCurveMidY.value - bottomLeftY
      const currentDiffRight = bottomCurveMidY.value - bottomRightY

      // 计算新的端点位置
      const newLeftY = newY - currentDiffLeft
      const newRightY = newY - currentDiffRight

      // 检查是否会超出限制
      if (
        newLeftY < topLeftY + MIN_VERTICAL_SPACING ||
        newRightY < topRightY + MIN_VERTICAL_SPACING
      ) {
        // 计算允许的最小Y值
        const minYLeft = topLeftY + MIN_VERTICAL_SPACING + currentDiffLeft
        const minYRight = topRightY + MIN_VERTICAL_SPACING + currentDiffRight
        newY = Math.max(minYLeft, minYRight)
      }
    }

    return { x: currentMidX.value, y: newY }
  }
}

/**
 * 创建宽度控制锚点拖动边界函数
 * @param isLeftAnchor 是否为左侧宽度控制锚点
 * @param currentMidX 当前中点X坐标
 * @param widthAnchorsY 宽度控制锚点的Y坐标
 * @returns 拖动边界函数
 */
export function createWidthAnchorDragBoundFunc(
  isLeftAnchor: boolean,
  currentMidX: ComputedRef<number>,
  widthAnchorsY: ComputedRef<number>
) {
  return function (pos: any) {
    // 只允许水平方向拖动，Y坐标固定
    let newX = pos.x

    if (isLeftAnchor) {
      // 左侧宽度控制锚点拖动限制
      // 确保不会太靠近中心点，保持最小宽度
      const maxX = currentMidX.value - MIN_HORIZONTAL_SPACING
      if (newX > maxX) {
        newX = maxX
      }
    } else {
      // 右侧宽度控制锚点拖动限制
      // 确保不会太靠近中心点，保持最小宽度
      const minX = currentMidX.value + MIN_HORIZONTAL_SPACING
      if (newX < minX) {
        newX = minX
      }
    }

    // 强制Y坐标保持不变，确保只能水平拖动
    return { x: newX, y: widthAnchorsY.value }
  }
}

/**
 * 框架锚点管理的组合式函数
 * 处理框架控制点的交互和拖动
 */
export function useFrameAnchors(
  frame: ComputedRef<TeethFrame>,
  dragUpdateScheduled: Ref<boolean>,
  currentMidX: ComputedRef<number>,
  masterAnchorY: ComputedRef<number>
) {
  const teethStore = useTeethStore()

  // 控制点相关常量
  const ANCHOR_HOVER_STROKE_WIDTH_INCREASE = 1.5
  const ICON_HOVER_SCALE = 1.1

  // 事件处理函数：鼠标悬停在圆形锚点上
  function handleAnchorMouseEnter(e: KonvaEventObject<MouseEvent>) {
    document.body.style.cursor = 'ns-resize'
    const target = e.target as Konva.Circle
    target.strokeWidth(target.strokeWidth() + ANCHOR_HOVER_STROKE_WIDTH_INCREASE)
  }

  // 事件处理函数：鼠标离开圆形锚点
  function handleAnchorMouseLeave(e: KonvaEventObject<MouseEvent>) {
    document.body.style.cursor = 'default'
    const target = e.target as Konva.Circle
    target.strokeWidth(target.strokeWidth() - ANCHOR_HOVER_STROKE_WIDTH_INCREASE)
  }

  // 事件处理函数：鼠标悬停在图标锚点上
  function handleIconMouseEnter(e: KonvaEventObject<MouseEvent>) {
    const targetName = e.target.name()

    // 设置鼠标样式
    if (targetName.includes('Width')) {
      document.body.style.cursor = 'ew-resize'
    } else if (targetName === 'masterAnchor') {
      document.body.style.cursor = 'move'
    } else {
      document.body.style.cursor = 'ns-resize'
    }

    // 如果不是图像控制点，则应用缩放效果
    if (targetName !== 'masterAnchor') {
      e.target.scale({ x: ICON_HOVER_SCALE, y: ICON_HOVER_SCALE })
    } else {
      // 对于图像控制点，可以应用其他效果，如改变透明度
      e.target.opacity(0.8)
    }
  }

  // 事件处理函数：鼠标离开图标锚点
  function handleIconMouseLeave(e: KonvaEventObject<MouseEvent>) {
    document.body.style.cursor = 'default'

    const targetName = e.target.name()
    if (targetName !== 'masterAnchor') {
      e.target.scale({ x: 1, y: 1 })
    } else {
      // 恢复图像控制点的原始效果
      e.target.opacity(1)
    }
  }

  // 上曲线中点拖动开始
  function handleMidAnchor1DragStart(e: KonvaEventObject<DragEvent>) {
    e.target.setAttrs({
      initialY: e.target.y(),
      initialLeftAnchorY: frame.value.topCurvePoints.leftY + frame.value.y,
      initialRightAnchorY: frame.value.topCurvePoints.rightY + frame.value.y,
      initialMasterAnchorY: masterAnchorY.value
    })
  }

  // 处理上曲线中点拖动
  function handleMidAnchor1Drag(e: KonvaEventObject<DragEvent>) {
    const currentMidY = e.target.y()
    const startMidY = e.target.getAttr('initialY')
    if (isNaN(startMidY) || isNaN(currentMidY)) return

    const deltaY = currentMidY - startMidY
    const initialLeftY = e.target.getAttr('initialLeftAnchorY')
    const initialRightY = e.target.getAttr('initialRightAnchorY')
    if (isNaN(initialLeftY) || isNaN(initialRightY)) return

    // 计算新的端点位置
    const newLeftY = initialLeftY + deltaY
    const newRightY = initialRightY + deltaY

    // 更新上曲线中点和端点 - 使用同步更新而不是requestAnimationFrame
    teethStore.updateFrame({
      topCurvePoints: {
        leftY: newLeftY - frame.value.y,
        midY: currentMidY - frame.value.y,
        rightY: newRightY - frame.value.y
      }
    })

    // 更新主控制锚点位置
    const initialMasterY = e.target.getAttr('initialMasterAnchorY')
    if (!isNaN(initialMasterY)) {
      // 主控制锚点跟随上曲线中点移动
      const masterNode = e.target.getStage()?.findOne('.masterAnchor')
      if (masterNode) {
        masterNode.y(initialMasterY + deltaY)
      }
    }
  }

  // 下曲线中点拖动开始
  function handleMidAnchor2DragStart(e: KonvaEventObject<DragEvent>) {
    e.target.setAttrs({
      initialY: e.target.y(),
      initialLeftAnchorY: frame.value.bottomCurvePoints.leftY + frame.value.y,
      initialRightAnchorY: frame.value.bottomCurvePoints.rightY + frame.value.y
    })
  }

  // 处理下曲线中点拖动
  function handleMidAnchor2Drag(e: KonvaEventObject<DragEvent>) {
    const currentMidY = e.target.y()
    const startMidY = e.target.getAttr('initialY')
    if (isNaN(startMidY) || isNaN(currentMidY)) return

    const deltaY = currentMidY - startMidY
    const initialLeftY = e.target.getAttr('initialLeftAnchorY')
    const initialRightY = e.target.getAttr('initialRightAnchorY')
    if (isNaN(initialLeftY) || isNaN(initialRightY)) return

    // 计算新的端点位置
    const newLeftY = initialLeftY + deltaY
    const newRightY = initialRightY + deltaY

    // 更新下曲线中点和端点 - 使用同步更新而不是requestAnimationFrame
    teethStore.updateFrame({
      bottomCurvePoints: {
        leftY: newLeftY - frame.value.y,
        midY: currentMidY - frame.value.y,
        rightY: newRightY - frame.value.y
      }
    })
  }

  // 辅助函数：更新曲线的宽度
  function updateCurvesWidth(newLeftX: number, newRightX: number) {
    if (isNaN(newLeftX) || isNaN(newRightX) || newLeftX >= newRightX) {
      console.error('updateCurvesWidth: 无效的输入X坐标。', { newLeftX, newRightX })
      return
    }

    // 计算新的宽度和X坐标
    const newWidth = newRightX - newLeftX
    const newX = newLeftX

    // 使用requestAnimationFrame优化更新
    if (!dragUpdateScheduled.value) {
      dragUpdateScheduled.value = true
      requestAnimationFrame(() => {
        teethStore.updateFrame({
          x: newX,
          width: newWidth
        })
        dragUpdateScheduled.value = false
      })
    }
  }

  // 处理左侧宽度控制锚点拖动
  function handleLeftWidthAnchorDrag(e: KonvaEventObject<DragEvent>) {
    const handleX = e.target.x()
    if (isNaN(handleX)) return

    // 保持对称性，计算新的宽度
    const newHalfWidth = currentMidX.value - handleX
    const newRightX = currentMidX.value + newHalfWidth

    updateCurvesWidth(handleX, newRightX)
  }

  // 处理右侧宽度控制锚点拖动
  function handleRightWidthAnchorDrag(e: KonvaEventObject<DragEvent>) {
    const newRightX = e.target.x()
    if (isNaN(newRightX)) return

    // 保持对称性，计算新的宽度
    const newHalfWidth = newRightX - currentMidX.value
    const newLeftX = currentMidX.value - newHalfWidth

    updateCurvesWidth(newLeftX, newRightX)
  }

  // 主控制锚点拖动开始
  function handleMasterAnchorDragStart(e: KonvaEventObject<DragEvent>) {
    e.target.setAttrs({
      startX: e.target.x(),
      startY: e.target.y(),
      initialFrameX: frame.value.x,
      initialFrameY: frame.value.y
    })
  }

  // 处理主控制锚点拖动（整体移动）
  function handleMasterAnchorDrag(e: KonvaEventObject<DragEvent>) {
    const currentHandleX = e.target.x()
    const currentHandleY = e.target.y()
    const startHandleX = e.target.getAttr('startX')
    const startHandleY = e.target.getAttr('startY')

    if ([startHandleX, startHandleY, currentHandleX, currentHandleY].some(isNaN)) {
      e.target.setAttrs({ startX: currentHandleX, startY: currentHandleY })
      return
    }

    const deltaX = currentHandleX - startHandleX
    const deltaY = currentHandleY - startHandleY
    const initialFrameX = e.target.getAttr('initialFrameX')
    const initialFrameY = e.target.getAttr('initialFrameY')

    if (!dragUpdateScheduled.value) {
      dragUpdateScheduled.value = true
      requestAnimationFrame(() => {
        teethStore.updateFrame({
          x: initialFrameX + deltaX,
          y: initialFrameY + deltaY
        })
        dragUpdateScheduled.value = false
      })
    }
  }

  return {
    // 常量
    ANCHOR_HOVER_STROKE_WIDTH_INCREASE,
    ICON_HOVER_SCALE,

    // 方法
    handleAnchorMouseEnter,
    handleAnchorMouseLeave,
    handleIconMouseEnter,
    handleIconMouseLeave,
    handleMidAnchor1DragStart,
    handleMidAnchor1Drag,
    handleMidAnchor2DragStart,
    handleMidAnchor2Drag,
    updateCurvesWidth,
    handleLeftWidthAnchorDrag,
    handleRightWidthAnchorDrag,
    handleMasterAnchorDragStart,
    handleMasterAnchorDrag
  }
}
