import { ref } from 'vue'
import { useCommonStore } from '@/store/common'
import { storeToRefs } from 'pinia'

// 全局配置常量
export const TARGET_RATIO = 0.5 // 牙齿区域占画布比例

/**
 * 管理画布尺寸和基本配置
 */
export function useCanvasStage() {
  // 从store获取数据
  const commonStore = useCommonStore()
  const { headerHeight, faceRotation } = storeToRefs(commonStore)

  // 舞台尺寸
  const stageWidth = ref(window.innerWidth)
  const stageHeight = ref(window.innerHeight - headerHeight.value)

  // 更新舞台尺寸
  function updateStageSize(container: HTMLElement | null, onSizeChange?: () => void) {
    if (container) {
      const oldWidth = stageWidth.value
      const oldHeight = stageHeight.value

      // 更新舞台尺寸
      stageWidth.value = container.clientWidth
      stageHeight.value = container.clientHeight

      // 如果尺寸发生变化，调用回调
      if (oldWidth !== stageWidth.value || oldHeight !== stageHeight.value) {
        onSizeChange?.()
      }
    }
  }

  return {
    stageWidth,
    stageHeight,
    headerHeight,
    faceRotation,
    updateStageSize
  }
}
