import { ref, onUnmounted } from 'vue'

/**
 * 通过鼠标滚轮对图片进行缩放/拖拽的逻辑
 * 使用requestAnimationFrame优化连续操作，减少不必要的渲染
 */

export interface UseImageTransformByWheelOptions {
  minScale?: number
  maxScale?: number
  scaleStep?: number
  getImageSize: () => { width: number; height: number }
  onTransformUpdate?: (params: { imgScale: number; offsetX: number; offsetY: number }) => void
}

export function useImageTransformByWheel(options: UseImageTransformByWheelOptions) {
  const {
    minScale = 0.05,
    maxScale = 5,
    scaleStep = 1.1,
    getImageSize,
    onTransformUpdate
  } = options

  const imgScale = ref(1)
  const offsetX = ref(0)
  const offsetY = ref(0)
  const isPanning = ref(false)
  const panStart = ref({ x: 0, y: 0 })
  const panOffsetStart = ref({ x: 0, y: 0 })

  // 用于存储requestAnimationFrame的ID
  let wheelRafId: number | null = null
  let panRafId: number | null = null

  // 组件卸载时清除所有动画帧请求
  onUnmounted(() => {
    if (wheelRafId !== null) {
      cancelAnimationFrame(wheelRafId)
      wheelRafId = null
    }
    if (panRafId !== null) {
      cancelAnimationFrame(panRafId)
      panRafId = null
    }
  })

  function triggerUpdate() {
    onTransformUpdate?.({
      imgScale: imgScale.value,
      offsetX: offsetX.value,
      offsetY: offsetY.value
    })
  }

  function handleWheel(e: WheelEvent, containerRect?: DOMRect) {
    e.preventDefault()
    const { width: imgW, height: imgH } = getImageSize()
    if (!imgW || !imgH) return

    // 立即获取事件相关数据，避免在异步回调中使用事件对象
    const rect = containerRect || (e.currentTarget as HTMLElement).getBoundingClientRect()
    const mouseX = e.clientX - rect.left
    const mouseY = e.clientY - rect.top
    const deltaY = e.deltaY
    const prevScale = imgScale.value
    let newScale = prevScale

    // 计算新的缩放比例
    if (deltaY < 0) {
      newScale = Math.min(maxScale, prevScale * scaleStep)
    } else {
      newScale = Math.max(minScale, prevScale / scaleStep)
    }

    if (newScale === prevScale) return

    // 如果已经有一个动画帧请求，取消它
    if (wheelRafId !== null) {
      cancelAnimationFrame(wheelRafId)
    }

    // 使用requestAnimationFrame优化渲染
    wheelRafId = requestAnimationFrame(() => {
      // 以图片左上角为锚点的逆变换，算出鼠标在图片坐标系下的点
      const imgX = (mouseX - offsetX.value) / prevScale
      const imgY = (mouseY - offsetY.value) / prevScale
      // 缩放后，调整offset使鼠标下内容不变
      offsetX.value = mouseX - imgX * newScale
      offsetY.value = mouseY - imgY * newScale
      imgScale.value = newScale
      triggerUpdate()
      wheelRafId = null
    })
  }

  function handleMouseDown(e: MouseEvent) {
    if (e.button === 1) {
      e.preventDefault()
      isPanning.value = true
      panStart.value = { x: e.clientX, y: e.clientY }
      panOffsetStart.value = { x: offsetX.value, y: offsetY.value }
      document.body.style.cursor = 'grab'
    }
  }

  function handleMouseMove(e: MouseEvent) {
    if (isPanning.value) {
      // 立即获取事件相关数据，避免在异步回调中使用事件对象
      const clientX = e.clientX
      const clientY = e.clientY

      // 如果已经有一个动画帧请求，取消它
      if (panRafId !== null) {
        cancelAnimationFrame(panRafId)
      }

      // 使用requestAnimationFrame优化渲染
      panRafId = requestAnimationFrame(() => {
        const dx = clientX - panStart.value.x
        const dy = clientY - panStart.value.y
        offsetX.value = panOffsetStart.value.x + dx
        offsetY.value = panOffsetStart.value.y + dy
        triggerUpdate()
        panRafId = null
      })
    }
  }

  function handleMouseUp(e: MouseEvent) {
    if (isPanning.value && e.button === 1) {
      isPanning.value = false
      document.body.style.cursor = ''

      // 取消所有动画帧请求
      if (panRafId !== null) {
        cancelAnimationFrame(panRafId)
        panRafId = null
      }
    }
  }

  return {
    imgScale,
    offsetX,
    offsetY,
    isPanning,
    handleWheel,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp
  }
}
