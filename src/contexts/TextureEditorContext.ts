import { InjectionKey, Ref, provide, inject } from 'vue'
import type { TransformParams } from '@/utils/transform2d'

/**
 * 纹理编辑器上下文接口定义
 * 专注于纹理页面所需的状态和方法
 */
export interface TextureEditorContext {
  // ===== 画布渲染相关状态 =====

  // 画布尺寸
  stageWidth: Ref<number>
  stageHeight: Ref<number>

  // 图片对象 - 纹理页面只需要微笑照
  smileImgObj: Ref<HTMLImageElement | null>

  // 图片原始尺寸
  smileImgNaturalWidth: Ref<number>
  smileImgNaturalHeight: Ref<number>

  // 图片变换参数
  imgScale: Ref<number>
  imgCenter: Ref<{ x: number; y: number }>
  faceRotation: Ref<number>

  // 布局相关
  headerHeight: Ref<number>

  // ===== 纹理特有状态 =====

  // 显示选项
  showMidline: Ref<boolean>
  showSmileFrame: Ref<boolean>

  // ===== 局部框架状态 =====

  // 局部框架位置和尺寸
  localFrameX: Ref<number>
  localFrameY: Ref<number>
  localFrameWidth: Ref<number>
  localFrameHeight: Ref<number>
  localFrameTopCurvePoints: Ref<{ leftY: number; midY: number; rightY: number }>
  localFrameBottomCurvePoints: Ref<{ leftY: number; midY: number; rightY: number }>
  hasInitializedLocalFrame: Ref<boolean>

  // ===== 画布交互方法 =====

  getTransformParams: () => TransformParams
  autoFocusToTeethArea: () => void
  handleWheel: (e: WheelEvent) => void
  handleMouseDown: (e: MouseEvent) => void
  handleMouseMove: (e: MouseEvent) => void
  handleMouseUp: (e: MouseEvent) => void
  updateStageSize: (container: HTMLElement | null) => void
  resetInteraction: () => void

  // ===== 局部框架方法 =====

  // 更新局部框架位置
  updateLocalFramePosition: () => void

  // 初始化局部框架
  initLocalFrame: (frameData: any) => void
}

// 创建注入键
export const TextureEditorKey: InjectionKey<TextureEditorContext> = Symbol('TextureEditorContext')

/**
 * 提供纹理编辑器上下文
 * @param context 纹理编辑器上下文对象
 */
export function provideTextureEditorContext(context: TextureEditorContext) {
  provide(TextureEditorKey, context)
}

/**
 * 注入并使用纹理编辑器上下文
 * @returns 纹理编辑器上下文对象
 */
export function useTextureEditorContext(): TextureEditorContext {
  const context = inject(TextureEditorKey)
  if (!context) {
    throw new Error('useTextureEditorContext must be used within a provider')
  }
  return context
}
