<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useStepStore } from '@/store/step'

// 步骤数据结构
interface Step {
  name: string
  label: string
  desc: string
  route: string
  iconPrefix: string
}

const steps: Step[] = [
  {
    name: 'import-image',
    label: '导入照片',
    desc: '导入或选择照片\n请拖动或点击以上载微笑和牵开器照片，清晰的微笑照有助于获得更好的结果',
    route: '/import-image',
    iconPrefix: 'step1'
  },
  {
    name: 'face-landmark',
    label: '定位关键点',
    desc: '点&旋转\n移动点并摆正照片',
    route: '/face-landmark',
    iconPrefix: 'step2'
  },
  {
    name: 'lip-edit',
    label: '唇线编辑',
    desc: '唇线编辑\n添加或移动点，让唇线贴合嘴唇',
    route: '/lip-edit',
    iconPrefix: 'step3'
  },
  {
    name: 'align',
    label: '照片对齐',
    desc: '照片对齐\n移动点以对齐，或者手动添加点以对齐',
    route: '/align',
    iconPrefix: 'step4'
  },
  {
    name: 'veneer-adjust',
    label: '设计微笑',
    desc: '设计微笑\n请设计微笑',
    route: '/veneer-adjust',
    iconPrefix: 'step5'
  },
  {
    name: 'visual-optimize',
    label: '选择纹理',
    desc: '选择纹理\n选择或编辑纹理',
    route: '/visual-optimize',
    iconPrefix: 'step6'
  },
  {
    name: 'retouch-tools',
    label: '精细调整',
    desc: '印章工具\n选择区域颜色，并覆盖',
    route: '/retouch-tools',
    iconPrefix: 'step7'
  },
  {
    name: 'result-share',
    label: '结果预览与保存',
    desc: '保存结果\n照片匿名，滑动线条以对比，保存照片',
    route: '/result-share',
    iconPrefix: 'step8'
  }
]

const router = useRouter()
const route = useRoute()
const stepStore = useStepStore()
const { activeIndex, maxActiveIndex } = storeToRefs(stepStore)

// 鼠标悬停的步骤索引
const hoverIndex = ref<number | null>(null)

// 初始化时根据路由同步Pinia状态
const syncStepByRoute = () => {
  const idx = steps.findIndex((step) => route.path.startsWith(step.route))
  if (idx !== -1) stepStore.setActiveIndex(idx)
}
onMounted(syncStepByRoute)
watch(() => route.path, syncStepByRoute)

// 步骤状态：已完成/当前/可选/禁用
const getStepStatus = (idx: number) => {
  if (idx < activeIndex.value) return 'normal' // 已完成
  if (idx === activeIndex.value) return 'active' // 当前
  if (idx <= maxActiveIndex.value + 1) return 'normal' // 可选（下一个或回退后已激活过的步骤）
  return 'disable' // 禁用
}

// 步骤是否可点击
const isStepClickable = (idx: number) => {
  return idx <= maxActiveIndex.value + 1
}

// 点击步骤
const handleStepClick = (idx: number) => {
  if (!isStepClickable(idx)) return
  stepStore.setActiveIndex(idx)
  router.push(steps[idx]?.route || '')
}
</script>

<template>
  <nav class="step-bar">
    <div class="step-bar__container">
      <div
        v-for="(step, idx) in steps"
        :key="step.name"
        class="step-bar__item"
        :class="[
          `step-bar__item--${getStepStatus(idx)}`,
          { 'is-hover': hoverIndex === idx, clickable: isStepClickable(idx) }
        ]"
        @mouseenter="hoverIndex = idx"
        @mouseleave="hoverIndex = null"
        @click="handleStepClick(idx)"
        :tabindex="isStepClickable(idx) ? 0 : -1"
        :aria-disabled="!isStepClickable(idx)"
        :style="isStepClickable(idx) ? 'cursor:pointer' : 'cursor:not-allowed'"
      >
        <img
          class="step-bar__icon"
          :src="`/src/assets/images/step-icons/${step.iconPrefix}-${getStepStatus(idx)}.png`"
          :alt="step.label"
          width="50"
          height="50"
        />
        <span class="step-bar__label">{{ step.label }}</span>
        <div v-if="hoverIndex !== null && hoverIndex === idx && step.desc" class="step-bar__desc">
          <span v-for="line in step.desc ? step.desc.split('\\n') : []" :key="line">{{
            line
          }}</span>
        </div>
      </div>
    </div>
  </nav>
</template>

<style lang="less" scoped>
.step-bar {
  width: 100%;
  background: #f7f7f7;
  padding: 12px 0;
  display: flex;
  justify-content: center;
  user-select: none;
  z-index: 100;

  &__container {
    display: flex;
    gap: 24px;
    align-items: center;
    flex-wrap: wrap;
  }

  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    cursor: pointer;
    outline: none;
    min-width: 60px;
    padding: 8px;
    transition: background 0.2s;
    border-radius: 8px;

    img {
      border-radius: 8px;
    }

    &--disable {
      cursor: not-allowed;
      opacity: 0.4;
    }

    &--active {
      //   border: 2px solid #409eff;
      background: #e6f0ff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.08);
    }

    &--normal {
      cursor: pointer;
      opacity: 1;
    }

    &.is-hover .step-bar__desc {
      display: block;
    }
  }

  &__icon {
    width: 50px;
    height: 50px;
    margin-bottom: 4px;
    display: block;
  }

  &__label {
    font-size: 14px;
    color: #333;
    text-align: center;
    white-space: nowrap;
  }

  &__desc {
    display: none;
    position: absolute;
    left: 0%;
    top: 96px;
    // transform: translateX(-50%);
    min-width: 160px;
    background: #dbe8f7;
    color: #222;
    font-size: 13px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 10px 14px;
    z-index: 100;
    text-align: left;
    line-height: 1.5;
    white-space: pre-line;
  }
}

@media (max-width: 700px) {
  .step-bar__container {
    gap: 10px;
  }

  .step-bar__desc {
    min-width: 120px;
    font-size: 12px;
    padding: 8px 8px;
  }

  .step-bar__label {
    font-size: 12px;
  }
}
</style>
