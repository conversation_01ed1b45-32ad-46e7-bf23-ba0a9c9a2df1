<template>
  <div
    ref="containerRef"
    class="align-preview-block"
    @wheel.prevent="handleWheelWithFlag"
    @mousedown="handleMouseDownWithFlag"
    @mousemove="handleMouseMove"
    @mouseup="handleMouseUp"
    @mouseleave="handleMouseUp"
    @contextmenu.prevent
  >
    <v-stage v-if="smileImgObj && mouthImgObj" :config="{ width, height }">
      <v-layer>
        <!-- 微笑照底图（独立聚焦，应用faceRotation旋转） -->
        <v-image
          :image="smileImgObj"
          :x="smileCenter.x"
          :y="smileCenter.y"
          :offsetX="smileImgNaturalWidth / 2"
          :offsetY="smileImgNaturalHeight / 2"
          :scaleX="smileScale"
          :scaleY="smileScale"
          :rotation="(faceRotation * 180) / Math.PI"
        />
        <!-- 中线（始终垂直，x=midlineStageX） -->
        <v-line
          v-if="showMidline"
          :points="[midlineStageX, 0, midlineStageX, height]"
          stroke="#ff6b6b"
          :strokeWidth="2"
          :dash="[8, 6]"
        />
        <!-- 开口照叠加，只有 alignParams 存在时才渲染 -->
        <v-image
          v-if="
            mouthRenderParams &&
            mouthImgObj &&
            mouthAlignPoints?.length >= 2 &&
            smileAlignPoints.length >= 2
          "
          :image="mouthImgObj"
          :x="mouthRenderParams.x"
          :y="mouthRenderParams.y"
          :offsetX="mouthImgNaturalWidth / 2"
          :offsetY="mouthImgNaturalHeight / 2"
          :scaleX="mouthRenderParams.scale"
          :scaleY="mouthRenderParams.scale"
          :rotation="(mouthRenderParams.angle * 180) / Math.PI"
          :opacity="opacity"
        />
      </v-layer>
    </v-stage>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted, computed, nextTick } from 'vue'
import { useImageTransformByWheel } from '@/composables/useImageTransformByWheel'
import { useCommonStore } from '@/store/common'
import { storeToRefs } from 'pinia'
import {
  imageToStage as imageToStageUtil,
  TransformParams,
  calcLipFocusScaleAndOffset as calcLipFocusScaleAndOffsetUtil
} from '@/utils/transform2d'
import { calcMidlineXOnStage } from '@/utils/faceMath'

const props = defineProps<{
  smileImage: string | null
  mouthImage: string | null
  smileLipPoints: { x: number; y: number }[]
  mouthLipPoints: { x: number; y: number }[]
  mouthAlignPoints: { x: number; y: number }[]
  smileAlignPoints: { x: number; y: number }[]
  mouthToSmileTransform: {
    angle: number
    scale: number
    offsetX: number
    offsetY: number
  } | null
  opacity?: number
}>()

// 从 store 获取 faceRotation
const commonStore = useCommonStore()
const { faceRotation } = storeToRefs(commonStore)

// 是否显示中线
const showMidline = ref(true)

const containerRef = ref<HTMLElement>()
const width = ref(600)
const height = ref(600)

// 微笑照图片对象及原始尺寸
const smileImgObj = ref<HTMLImageElement | null>(null)
const smileImgNaturalWidth = ref(0)
const smileImgNaturalHeight = ref(0)
// 开口照图片对象及原始尺寸
const mouthImgObj = ref<HTMLImageElement | null>(null)
const mouthImgNaturalWidth = ref(0)
const mouthImgNaturalHeight = ref(0)

// 牙齿区域聚焦参数
function calcLipFocusScaleAndOffset(
  lipPoints: { x: number; y: number }[],
  stageW: number,
  stageH: number,
  imgNaturalWidth: number,
  imgNaturalHeight: number
) {
  if (!lipPoints || lipPoints.length < 2)
    return { scale: 1, center: { x: stageW / 2, y: stageH / 2 } }

  // 使用工具函数，传入旋转角度和图片尺寸
  const targetRatio = 0.5 // 牙齿区域占画布比例
  const result = calcLipFocusScaleAndOffsetUtil(lipPoints, stageW, stageH, {
    targetRatio,
    rotation: faceRotation.value,
    imgWidth: imgNaturalWidth,
    imgHeight: imgNaturalHeight,
    returnCenter: true
  })

  return {
    scale: result.scale,
    center: result.center || {
      x: stageW / 2,
      y: stageH / 2
    }
  }
}

// 统一缩放/拖拽逻辑（以smile图为锚点）
const {
  imgScale: smileImgScale,
  offsetX: smileOffsetX,
  offsetY: smileOffsetY,
  handleWheel,
  handleMouseDown,
  handleMouseMove,
  handleMouseUp
} = useImageTransformByWheel({
  minScale: 0.05,
  maxScale: 3,
  scaleStep: 1.1,
  getImageSize: () => ({ width: smileImgNaturalWidth.value, height: smileImgNaturalHeight.value }),
  onTransformUpdate: () => {}
})

// 响应式 smileScale、smileCenter
const smileScale = computed(() => smileImgScale.value)
const smileCenter = computed(() => ({
  x: smileOffsetX.value + (smileImgNaturalWidth.value * smileImgScale.value) / 2,
  y: smileOffsetY.value + (smileImgNaturalHeight.value * smileImgScale.value) / 2
}))

// 构造通用变换参数
function getTransformParams(): TransformParams {
  return {
    scale: smileImgScale.value,
    offsetX: smileOffsetX.value,
    offsetY: smileOffsetY.value,
    rotation: faceRotation.value,
    anchorX: smileImgNaturalWidth.value / 2,
    anchorY: smileImgNaturalHeight.value / 2
  }
}

// 中线在画布坐标系下的 x 坐标（不考虑旋转，始终垂直）
const midlineStageX = computed(() => {
  // 获取 store 中的面部关键点
  const smileFaceLandmarks = commonStore.smileFaceLandmarks

  // 定义图片坐标到画布坐标的转换函数
  const imageToStage = (pt: { x: number; y: number }) => {
    return imageToStageUtil(pt, getTransformParams())
  }

  // 使用新方法：先将各点转换到画布坐标系，再计算中点
  return calcMidlineXOnStage(smileFaceLandmarks, imageToStage)
})

const opacity = computed(() => props.opacity ?? 0.5)

/**
 * 计算开口照在画布上的实际渲染参数
 *
 * 目标：
 *   使得开口照的中心点经过变换后，在画布上与微笑照的对应点重合。
 *
 * 变换流程：
 *   1. 计算开口照中心点在微笑照坐标系中的位置
 *   2. 使用 imageToStageUtil 将该点转换到画布坐标系
 *   3. 返回画布坐标系中的位置、缩放和旋转角度
 */
const mouthRenderParams = computed(() => {
  if (
    !props.mouthToSmileTransform ||
    !props.mouthAlignPoints?.length ||
    !props.smileAlignPoints?.length
  )
    return null

  const { angle, scale, offsetX, offsetY } = props.mouthToSmileTransform

  // 开口照中心点在开口照坐标系中的位置
  const mouthCenterX = mouthImgNaturalWidth.value / 2
  const mouthCenterY = mouthImgNaturalHeight.value / 2

  // 将开口照中心点变换到微笑照坐标系
  // 1. 缩放
  const scaledX = mouthCenterX * scale
  const scaledY = mouthCenterY * scale

  // 2. 旋转
  const cosA = Math.cos(angle)
  const sinA = Math.sin(angle)
  const rotatedX = scaledX * cosA - scaledY * sinA
  const rotatedY = scaledX * sinA + scaledY * cosA

  // 3. 平移
  const mouthCenterInSmile = {
    x: rotatedX + offsetX,
    y: rotatedY + offsetY
  }

  // 将微笑照坐标系中的点转换到画布坐标系
  const transformParams = {
    scale: smileImgScale.value,
    offsetX: smileOffsetX.value,
    offsetY: smileOffsetY.value,
    rotation: faceRotation.value,
    anchorX: smileImgNaturalWidth.value / 2,
    anchorY: smileImgNaturalHeight.value / 2
  }

  const stagePoint = imageToStageUtil(mouthCenterInSmile, transformParams)

  // 开口照的总旋转角度 = 开口照相对于微笑照的旋转角度 + 微笑照的旋转角度
  const totalAngle = angle + faceRotation.value

  return {
    x: stagePoint.x,
    y: stagePoint.y,
    scale: scale * smileImgScale.value,
    angle: totalAngle
  }
})

// 自动聚焦逻辑
const hasUserInteracted = ref(false)
function autoFocus() {
  // 只在用户未手动缩放/拖拽时自动聚焦
  if (hasUserInteracted.value) return
  const { scale, center } = calcLipFocusScaleAndOffset(
    props.smileLipPoints,
    width.value,
    height.value,
    smileImgNaturalWidth.value,
    smileImgNaturalHeight.value
  )
  smileImgScale.value = scale
  smileOffsetX.value = center.x - (smileImgNaturalWidth.value * scale) / 2
  smileOffsetY.value = center.y - (smileImgNaturalHeight.value * scale) / 2
}

// 监听图片、点集和旋转角度变化，自动聚焦
watch(
  [smileImgNaturalWidth, smileImgNaturalHeight, () => props.smileLipPoints, faceRotation],
  async () => {
    await nextTick()
    if (
      smileImgNaturalWidth.value &&
      smileImgNaturalHeight.value &&
      props.smileLipPoints &&
      props.smileLipPoints.length > 1
    ) {
      // 重置用户交互标记，确保旋转时能重新聚焦
      if (faceRotation.value && Math.abs(faceRotation.value) > 0.01) {
        hasUserInteracted.value = false
      }
      autoFocus()
    }
  }
)

// 监听用户交互，标记 hasUserInteracted
function handleWheelWithFlag(e: WheelEvent) {
  hasUserInteracted.value = true
  handleWheel(e)
}
function handleMouseDownWithFlag(e: MouseEvent) {
  if (e.button === 1) hasUserInteracted.value = true
  handleMouseDown(e)
}

function updateSize() {
  if (containerRef.value) {
    width.value = containerRef.value.clientWidth || 600
    height.value = containerRef.value.clientHeight || 600
  }
}
onMounted(() => {
  updateSize()
  const ro = new ResizeObserver(updateSize)
  if (containerRef.value) ro.observe(containerRef.value)
})

// 加载图片
watch(
  () => props.smileImage,
  (src) => {
    if (!src) return
    const img = new window.Image()
    img.src = src
    img.onload = () => {
      smileImgObj.value = img
      smileImgNaturalWidth.value = img.width
      smileImgNaturalHeight.value = img.height
    }
  },
  { immediate: true }
)
watch(
  () => props.mouthImage,
  (src) => {
    if (!src) return
    const img = new window.Image()
    img.src = src
    img.onload = () => {
      mouthImgObj.value = img
      mouthImgNaturalWidth.value = img.width
      mouthImgNaturalHeight.value = img.height
    }
  },
  { immediate: true }
)
</script>
<style scoped>
.align-preview-block {
  width: 100%;
  height: 100%;
  background: #fff;
  position: relative;
  box-shadow: 0 2px 16px #0001;
}
</style>
