<template>
  <div
    ref="containerRef"
    class="align-img-block"
    @wheel.prevent="handleWheel"
    @mousedown="handleMouseDown"
    @mousemove="handleMouseMove"
    @mouseup="handleMouseUp"
    @mouseleave="handleMouseUp"
    @contextmenu.prevent
  >
    <v-stage
      v-if="imgObj"
      :config="{ width, height }"
      @click="handleStageClick"
      style="cursor: pointer"
    >
      <v-layer>
        <v-image
          :image="imgObj"
          :x="imgCenter.x"
          :y="imgCenter.y"
          :offsetX="imgNaturalWidth / 2"
          :offsetY="imgNaturalHeight / 2"
          :scaleX="imgScale"
          :scaleY="imgScale"
          :rotation="isSmileImage ? (faceRotation * 180) / Math.PI : 0"
        />
        <!-- 中线（仅在微笑照上显示，始终垂直） -->
        <v-line
          v-if="isSmileImage && showMidline"
          :points="[midlineStageX, 0, midlineStageX, height]"
          stroke="#ff6b6b"
          :strokeWidth="2"
          :dash="[8, 6]"
        />
        <v-circle
          v-for="(p, idx) in renderPoints"
          :key="idx"
          :x="transformPoint(p).x"
          :y="transformPoint(p).y"
          :radius="pointRadius"
          :fill="getPointColor(idx, hoverIdx === idx)"
          :stroke="'#fff'"
          :strokeWidth="2"
          :opacity="getPointOpacity(hoverIdx === idx)"
          :config="{
            draggable: true
          }"
          @dragmove="handlePointDrag($event, idx)"
          @mouseenter="hoverIdx = idx"
          @mouseleave="hoverIdx = null"
        />
      </v-layer>
    </v-stage>
    <div class="align-img-label">{{ label }}</div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, onActivated, onDeactivated, computed } from 'vue'
import type Konva from 'konva'
import {
  imageToStage as imageToStageUtil,
  stageToImage as stageToImageUtil,
  TransformParams,
  calcLipFocusScaleAndOffset as calcLipFocusScaleAndOffsetUtil
} from '@/utils/transform2d'
import { useImageTransformByWheel } from '@/composables/useImageTransformByWheel'
import { useCommonStore } from '@/store/common'
import { storeToRefs } from 'pinia'
import { calcMidlineXOnStage } from '@/utils/faceMath'

const props = defineProps<{
  imgSrc: string | null
  points: { x: number; y: number }[]
  label: string
  lipPoints?: { x: number; y: number }[]
  onPointSelect?: (pt: { x: number; y: number }) => void
  isSelecting?: boolean
  selectingIndex?: number
  onPointMove?: (idx: number, pt: { x: number; y: number }) => void
  isSmileImage?: boolean // 是否为微笑照
}>()

const containerRef = ref<HTMLElement>()
const width = ref(0)
const height = ref(0)
const imgObj = ref<HTMLImageElement | null>(null)
const imgNaturalWidth = ref(0)
const imgNaturalHeight = ref(0)
const imgCenter = ref({ x: 0, y: 0 })
const baseScale = ref(1)
const hoverIdx = ref<number | null>(null)

// 从 store 获取 faceRotation
const commonStore = useCommonStore()
const { faceRotation } = storeToRefs(commonStore)

// 是否显示中线
const showMidline = ref(true)

// 中线在画布坐标系下的 x 坐标（不考虑旋转，始终垂直）
const midlineStageX = computed(() => {
  if (!props.isSmileImage) return 0

  // 获取 store 中的面部关键点
  const smileFaceLandmarks = commonStore.smileFaceLandmarks

  // 使用新方法：先将各点转换到画布坐标系，再计算中点
  return calcMidlineXOnStage(smileFaceLandmarks, transformPoint)
})

const targetRatio = 0.7 // 唇线包围盒占容器的比例

// 让唇线区域适配容器的缩放与偏移
function calcLipFocusScaleAndOffset(
  lipPoints: { x: number; y: number }[],
  stageW: number,
  stageH: number
) {
  if (!lipPoints || lipPoints.length < 2) return { scale: 1, offsetX: 0, offsetY: 0 }

  // 使用工具函数，传入旋转角度和图片尺寸
  // 只有微笑照才应用旋转
  return calcLipFocusScaleAndOffsetUtil(lipPoints, stageW, stageH, {
    targetRatio,
    rotation: props.isSmileImage ? faceRotation.value : 0,
    imgWidth: imgNaturalWidth.value,
    imgHeight: imgNaturalHeight.value
  })
}

const lipFocusParams = computed(() =>
  calcLipFocusScaleAndOffset(props.lipPoints || [], width.value, height.value)
)

function updateSize() {
  if (containerRef.value) {
    width.value = containerRef.value.clientWidth
    height.value = containerRef.value.clientHeight
  }
}

// 创建一个引用来存储 ResizeObserver 实例
const resizeObserver = ref<ResizeObserver | null>(null)

// 初始化 ResizeObserver
function setupResizeObserver() {
  // 确保先清理之前的 observer
  cleanupResizeObserver()

  // 创建新的 observer
  resizeObserver.value = new ResizeObserver(updateSize)
  if (containerRef.value) {
    resizeObserver.value.observe(containerRef.value)
  }
}

// 清理 ResizeObserver
function cleanupResizeObserver() {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
    resizeObserver.value = null
  }
}

onMounted(() => {
  updateSize()
  setupResizeObserver()
})

// 当组件从缓存中重新激活时调用
onActivated(() => {
  updateSize() // 确保尺寸是最新的
  setupResizeObserver() // 重新设置 ResizeObserver
})

// 当组件被 keep-alive 缓存但不活跃时调用
onDeactivated(() => {
  cleanupResizeObserver()
})

// 当组件完全卸载时调用
onUnmounted(() => {
  cleanupResizeObserver()
})

// 自动缩放与居中
function updateImageAndFocus() {
  if (!imgObj.value || !props.lipPoints || props.lipPoints.length < 2) return

  // 重新计算变换参数，考虑旋转因素
  const { scale, offsetX: ox, offsetY: oy } = lipFocusParams.value

  // 更新缩放和偏移
  imgScale.value = scale
  baseScale.value = scale // 记录基准缩放
  offsetX.value = ox
  offsetY.value = oy

  // 更新图片中心位置
  imgCenter.value = {
    x: ox + (imgNaturalWidth.value * scale) / 2,
    y: oy + (imgNaturalHeight.value * scale) / 2
  }

  // 如果是微笑照且有旋转，可能需要额外调整以确保唇线区域在画布中心
  if (props.isSmileImage && faceRotation.value && Math.abs(faceRotation.value) > 0.01) {
    // 在下一帧再次更新，确保旋转后的位置正确
    requestAnimationFrame(() => {
      const { scale, offsetX: ox, offsetY: oy } = lipFocusParams.value
      offsetX.value = ox
      offsetY.value = oy
      imgCenter.value = {
        x: ox + (imgNaturalWidth.value * scale) / 2,
        y: oy + (imgNaturalHeight.value * scale) / 2
      }
    })
  }
}

watch([() => props.lipPoints, width, height, imgObj, faceRotation], () => {
  updateImageAndFocus()
})

watch(
  () => props.imgSrc,
  (src) => {
    if (!src) return
    const img = new window.Image()
    img.src = src
    img.onload = () => {
      console.log('img onload', img.width, img.height)
      imgObj.value = img
      imgNaturalWidth.value = img.width
      imgNaturalHeight.value = img.height
      updateImageAndFocus()
    }
  },
  { immediate: true }
)

// 构造通用变换参数
function getTransformParams(): TransformParams {
  return {
    scale: imgScale.value,
    offsetX: imgCenter.value.x - (imgNaturalWidth.value / 2) * imgScale.value,
    offsetY: imgCenter.value.y - (imgNaturalHeight.value / 2) * imgScale.value,
    anchorX: imgNaturalWidth.value / 2,
    anchorY: imgNaturalHeight.value / 2,
    // 只有微笑照才应用旋转
    rotation: props.isSmileImage ? faceRotation.value : 0
  }
}

// 点变换
function transformPoint(p: { x: number; y: number }) {
  if (!imgObj.value) return { x: 0, y: 0 }
  return imageToStageUtil(p, getTransformParams())
}

// 逆变换
function inverseTransformPoint(pt: { x: number; y: number }) {
  if (!imgObj.value) return { x: 0, y: 0 }
  return stageToImageUtil(pt, getTransformParams())
}

const renderPoints = computed(() => props.points || [])

// 点半径随缩放自适应
const pointRadius = computed(() => {
  // 8为基准像素
  if (!imgScale.value || !baseScale.value) return 8
  return 8 * (imgScale.value / baseScale.value)
})

function handleStageClick(e: Konva.KonvaEventObject<MouseEvent>) {
  // 只响应鼠标左键点击 (button === 0)
  if (e.evt.button !== 0) return
  if (!props.isSelecting || !imgObj.value || !props.onPointSelect) return
  const stage = e.target.getStage()
  const pointerPos = stage?.getPointerPosition()
  if (!pointerPos) return
  const imgPt = inverseTransformPoint(pointerPos)
  props.onPointSelect(imgPt)
}

function handlePointDrag(e: Konva.KonvaEventObject<DragEvent>, idx: number) {
  if (!props.onPointMove) return
  // 使用 inverseTransformPoint 函数进行坐标转换，确保考虑旋转
  const stagePos = { x: e.target.x(), y: e.target.y() }
  const imgPt = inverseTransformPoint(stagePos)
  props.onPointMove(idx, imgPt)
}

function getPointColor(idx: number, isHover = false) {
  if (isHover) {
    // 悬停时高亮色
    return idx === 0 ? '#0056b3' : '#009e6d'
  }
  return idx === 0 ? '#1e90ff' : '#00c48f'
}

function getPointOpacity(isHover = false) {
  return isHover ? 1 : 0.7
}

// 统一缩放/拖拽逻辑
const { imgScale, offsetX, offsetY, handleWheel, handleMouseDown, handleMouseMove, handleMouseUp } =
  useImageTransformByWheel({
    minScale: 0.05,
    maxScale: 3,
    scaleStep: 1.1,
    getImageSize: () => ({ width: imgNaturalWidth.value, height: imgNaturalHeight.value }),
    onTransformUpdate: () => {
      imgCenter.value = {
        x: offsetX.value + (imgNaturalWidth.value * imgScale.value) / 2,
        y: offsetY.value + (imgNaturalHeight.value * imgScale.value) / 2
      }
    }
  })
</script>
<style scoped>
.align-img-block {
  flex: 1;
  background: #fff;
  box-shadow: 0 2px 16px #0001;
  position: relative;
  display: flex;
  height: calc((100% - 10px) / 2);
  align-items: center;
  justify-content: center;
}
.align-img-label {
  position: absolute;
  right: 16px;
  top: 16px;
  background: #fff9;
  border-radius: 6px;
  padding: 2px 12px;
  font-size: 16px;
  color: #333;
  pointer-events: none;
}
</style>
