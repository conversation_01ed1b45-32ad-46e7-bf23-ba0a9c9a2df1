<template>
  <v-group>
    <!-- 框架控制 - 根据showControls和showSmileFrame属性决定显示哪个版本的框架 -->
    <TeethFrameControl
      v-if="props.showSmileFrame && props.showControls"
      :stageScale="props.stageScale"
    />
    <TeethFrameSimple
      v-else-if="props.showSmileFrame && !props.showControls"
      :stageScale="props.stageScale"
    />

    <!-- 隐藏的框架 - 即使不显示框架，也需要保持框架的位置信息更新 -->
    <TeethFrameSimple
      v-if="!props.showSmileFrame"
      :stageScale="props.stageScale"
      :visible="false"
    />

    <!-- 牙齿轮廓渲染 -->
    <v-group v-if="segments.length > 0">
      <ToothOutline
        v-for="segment in segments"
        :key="`outline-${segment.index}`"
        :segmentInfo="segment"
        :toothId="segment.index"
        :selected="selectedToothId === segment.index"
        :listening="props.interactive"
      />
    </v-group>
  </v-group>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useTeethStore } from '@/store/teeth'
import TeethFrameControl from './TeethFrameControl.vue'
import TeethFrameSimple from './TeethFrameSimple.vue'
import ToothOutline from './ToothOutline.vue'

// Props
const props = defineProps({
  stageScale: {
    type: Number,
    default: 1
  },
  showControls: {
    type: Boolean,
    default: true
  },
  interactive: {
    type: Boolean,
    default: true
  },
  showSmileFrame: {
    type: Boolean,
    default: true
  }
})

// Store
const teethStore = useTeethStore()
const selectedToothId = computed(() => teethStore.selectedToothId)
const segments = computed(() => teethStore.segments)
</script>
