<template>
  <div
    class="image-uploader"
    :class="{ 'has-image': hasImage, dragging: isDragging }"
    @dragover="handleDragOver"
    @dragenter="handleDragEnter"
    @dragleave="handleDragLeave"
    @drop="handleDrop"
    @click="triggerFileInput"
  >
    <input
      ref="fileInput"
      type="file"
      class="file-input"
      :accept="acceptedFileTypes.join(',')"
      @change="handleFileChange"
    />

    <!-- 上传区域 -->
    <div v-if="!hasImage" class="upload-area">
      <div class="upload-icon">
        <img :src="iconSrc" alt="上传图标" class="icon-image" />
      </div>
      <div v-if="placeholder" class="upload-text">{{ placeholder }}</div>
      <div v-if="isDragging" class="drag-hint">
        <div class="drag-hint-text">松开鼠标释放文件</div>
        <div class="drag-hint-icon">
          <svg
            viewBox="0 0 24 24"
            width="24"
            height="24"
            stroke="currentColor"
            stroke-width="2"
            fill="none"
          >
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
          </svg>
        </div>
      </div>
    </div>

    <!-- 图片预览区域 -->
    <div v-else class="preview-area">
      <img :src="imageUrl" alt="预览图片" class="preview-image" />
      <div class="preview-overlay">
        <button class="reupload-btn" @click.stop="handleReupload">重新上传</button>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '点击或拖拽图片到此处'
  },
  iconSrc: {
    type: String,
    required: true
  },
  maxSizeMB: {
    type: Number,
    default: 10
  },
  acceptedFileTypes: {
    type: Array as () => string[],
    default: () => ['image/jpeg', 'image/png', 'image/gif']
  }
})

const emit = defineEmits(['update:modelValue', 'upload-success', 'upload-error'])

const fileInput = ref<HTMLInputElement | null>(null)
const imageUrl = ref<string>(props.modelValue)
const isDragging = ref(false)
const errorMessage = ref('')

// 是否已有图片
const hasImage = computed(() => !!imageUrl.value)

// 触发文件选择
const triggerFileInput = () => {
  if (fileInput.value) {
    fileInput.value.click()
  }
}

// 处理拖拽事件
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  // 确保拖拽的是文件
  if (e.dataTransfer) {
    e.dataTransfer.dropEffect = 'copy'
  }
  isDragging.value = true
}

const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  // 检查是否真的离开了元素（而不是进入子元素）
  const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
  const x = e.clientX
  const y = e.clientY

  // 如果鼠标位置在元素外部，则认为真的离开了
  if (x < rect.left || x >= rect.right || y < rect.top || y >= rect.bottom) {
    isDragging.value = false
  }
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = false

  if (e.dataTransfer?.files.length) {
    const file = e.dataTransfer.files[0]
    // 检查是否是图片文件
    if (file && file.type.startsWith('image/')) {
      handleFile(file)
    } else {
      errorMessage.value = '请上传图片文件'
      emit('upload-error', errorMessage.value)
    }
  }
}

// 处理文件选择
const handleFileChange = (e: Event) => {
  const target = e.target as HTMLInputElement
  if (target.files?.length) {
    const file = target.files[0]
    if (file) {
      handleFile(file)
    }
  }
}

// 处理文件上传
const handleFile = (file: File) => {
  // 清除之前的错误信息
  errorMessage.value = ''

  // 验证文件类型
  if (!props.acceptedFileTypes.includes(file.type)) {
    errorMessage.value = '不支持的文件类型，请上传JPG、PNG或GIF图片'
    emit('upload-error', errorMessage.value)
    return
  }

  // 验证文件大小
  const maxSizeBytes = props.maxSizeMB * 1024 * 1024
  if (file.size > maxSizeBytes) {
    errorMessage.value = `文件大小超过限制，最大${props.maxSizeMB}MB`
    emit('upload-error', errorMessage.value)
    return
  }

  // 读取文件并预览
  const reader = new FileReader()
  reader.onload = (e) => {
    const result = e.target?.result as string
    imageUrl.value = result
    emit('update:modelValue', result)
    emit('upload-success', result)
  }
  reader.onerror = () => {
    errorMessage.value = '读取文件失败，请重试'
    emit('upload-error', errorMessage.value)
  }
  reader.readAsDataURL(file)
}

// 重新上传
const handleReupload = () => {
  imageUrl.value = ''
  emit('update:modelValue', '')
  if (fileInput.value) {
    fileInput.value.value = ''
    // 立即拉起文件选择对话框
    setTimeout(() => {
      fileInput.value?.click()
    }, 0)
  }
}
</script>

<style lang="less" scoped>
.image-uploader {
  width: 100%;
  height: 100%;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: relative;
  transition: all 0.3s;
  background-color: #f5f7fa;
  overflow: hidden;

  &:hover {
    border-color: #409eff;
  }

  &.dragging {
    border-color: #409eff;
    border-width: 3px;
    background-color: rgba(64, 158, 255, 0.15);
    box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
    transform: scale(1.02);
  }

  &.has-image {
    border: none;

    &:hover .preview-overlay {
      opacity: 1;
    }
  }
}

.file-input {
  display: none;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  width: 100%;
  height: 100%;
}

.upload-icon {
  margin-bottom: 10px;

  .icon-image {
    width: 140px;
    height: 140px;
    object-fit: contain;
  }
}

.upload-text {
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.drag-hint {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  z-index: 10;
  animation: pulse 1.5s infinite;

  .drag-hint-text {
    font-size: 16px;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 10px;
  }

  .drag-hint-icon {
    color: #409eff;
    animation: bounce 1s infinite;
  }
}

@keyframes pulse {
  0% {
    background-color: rgba(64, 158, 255, 0.2);
  }
  50% {
    background-color: rgba(64, 158, 255, 0.3);
  }
  100% {
    background-color: rgba(64, 158, 255, 0.2);
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.preview-area {
  width: 100%;
  height: 100%;
  position: relative;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: #f5f7fa;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;

  // 只在悬停时显示，默认隐藏
  display: none;
  .image-uploader:hover & {
    display: flex;
  }
}

.reupload-btn {
  background-color: #fff;
  color: #409eff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background-color: #ecf5ff;
  }
}

.error-message {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 5px;
  background-color: rgba(245, 108, 108, 0.9);
  color: #fff;
  font-size: 12px;
  text-align: center;
}
</style>
