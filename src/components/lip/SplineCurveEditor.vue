<template>
  <v-group ref="groupRef" @click="handleGroupClick">
    <!-- 事件捕获用透明Rect，用于空白处点击添加点 -->
    <v-rect
      :config="{
        x: 0,
        y: 0,
        width: props.width,
        height: props.height,
        fill: 'rgba(0,0,0,0)',
        listening: true
      }"
    />
    <!-- 曲线 -->
    <v-line :config="curveConfig" ref="lineRef" />

    <!-- 控制点 - 仅在可编辑模式下显示 -->
    <template v-if="editable">
      <v-circle
        v-for="(anchor, index) in internalPoints"
        :key="index"
        :config="{
          x: anchor.x,
          y: anchor.y,
          radius:
            index === 0 && internalPoints.length > 2 && !internalClosed && anchor.isHovered
              ? actualPointRadius + 2 * props.scale
              : actualPointRadius,
          fill: index === 0 ? firstPointColor : pointColor,
          stroke:
            index === 0 && internalPoints.length > 2 && !internalClosed
              ? firstPointColor
              : pointStroke,
          strokeWidth: anchor.isHovered
            ? actualHighlightedPointStrokeWidth
            : actualPointStrokeWidth,
          draggable: allowDragPoints
        }"
        @dragmove="handleAnchorDragMove($event, index)"
        @mouseover="handleAnchorMouseOver(index)"
        @mouseout="handleAnchorMouseOut(index)"
        @click="(e: KonvaEventObject<MouseEvent>) => handleAnchorClick(index, e)"
      />
    </template>
  </v-group>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import type { Group } from 'konva/lib/Group'
import type { Shape } from 'konva/lib/Shape'
import type { KonvaEventObject } from 'konva/lib/Node'
import { getDistance, findCurveInsertionPoint, type Point } from '@/utils/curveUtils'

// 定义锚点类型
interface Anchor {
  x: number
  y: number
  isHovered: boolean
}

// 定义Props
interface Props {
  // 基础数据
  points: { x: number; y: number }[]
  closed?: boolean
  tension?: number
  editable?: boolean

  // 样式相关
  curveColor?: string
  curveWidth?: number
  pointRadius?: number
  pointColor?: string
  firstPointColor?: string
  pointStroke?: string
  pointStrokeWidth?: number
  highlightedPointStrokeWidth?: number

  // 交互相关
  allowAddPoints?: boolean
  allowAddPointsWhenClosed?: boolean
  allowDragPoints?: boolean
  detectThreshold?: number

  // 舞台配置
  width?: number
  height?: number
  scale?: number
}

// 定义Emits
const emit = defineEmits<{
  (e: 'update:points', value: { x: number; y: number }[]): void
  (e: 'update:closed', value: boolean): void
  (e: 'change', value: { x: number; y: number }[]): void
  (e: 'close'): void
  (e: 'open'): void
  (e: 'activate'): void
}>()

// Props默认值
const props = withDefaults(defineProps<Props>(), {
  closed: false,
  tension: 0.5, // 使用Konva推荐的张力值，提供平滑的曲线
  editable: true,
  curveColor: '#2080ff',
  curveWidth: 3,
  pointRadius: 8,
  pointColor: '#6b9aff',
  firstPointColor: '#ff6b6b',
  pointStroke: '#333',
  pointStrokeWidth: 2,
  highlightedPointStrokeWidth: 4,
  allowAddPoints: true,
  allowAddPointsWhenClosed: true,
  allowDragPoints: true,
  detectThreshold: 20,
  width: 800,
  height: 600,
  scale: 1
})

// 内部状态
const internalPoints = ref<Anchor[]>([])
const internalClosed = ref(props.closed)

// 图层引用
const groupRef = ref<{ getNode(): Group } | null>(null)
const lineRef = ref<{ getNode(): Shape } | null>(null)

// 计算实际视觉参数
const actualPointRadius = computed(() => props.pointRadius * props.scale)
const actualCurveWidth = computed(() => props.curveWidth * props.scale)
const actualPointStrokeWidth = computed(() => props.pointStrokeWidth * props.scale)
const actualHighlightedPointStrokeWidth = computed(
  () => props.highlightedPointStrokeWidth * props.scale
)
const actualDetectThreshold = computed(() => props.detectThreshold * props.scale)

// 初始化内部点数据
const initializePoints = () => {
  internalPoints.value = props.points.map((point) => ({
    x: point.x,
    y: point.y,
    isHovered: false
  }))
}

// 同步内部状态到外部
const syncPointsToParent = () => {
  const simplifiedPoints = internalPoints.value.map((point) => ({
    x: point.x,
    y: point.y
  }))
  emit('update:points', simplifiedPoints)
  emit('change', simplifiedPoints)
}

// 监听props变化
watch(
  () => props.points,
  () => {
    initializePoints()
  },
  { deep: true }
)

watch(
  () => props.closed,
  (newValue) => {
    internalClosed.value = newValue
  }
)

watch(
  () => internalClosed.value,
  (newValue) => {
    emit('update:closed', newValue)
    if (newValue) {
      emit('close')
    } else {
      emit('open')
    }
  }
)

// 监听张力系数变化，触发重绘
watch(
  () => props.tension,
  () => {
    // 手动触发图层重绘
    groupRef.value?.getNode()?.getLayer()?.batchDraw()
  }
)

// 监听编辑模式变化
watch(
  () => props.editable,
  (newValue) => {
    // 重置鼠标样式
    document.body.style.cursor = 'default'

    // 在切换到不可编辑模式时，清除所有控制点的悬停状态
    if (!newValue) {
      internalPoints.value.forEach((point) => {
        point.isHovered = false
      })
    }

    // 手动触发图层重绘
    groupRef.value?.getNode()?.getLayer()?.batchDraw()
  }
)

// 不再需要控制点缓存，因为使用了Konva的内置样条曲线功能

// 使用导入的getDistance函数

// 查找最近的曲线点并计算插入点
const findInsertionPoint = (point: {
  x: number
  y: number
}): { index: number; point: { x: number; y: number } } | null => {
  // 在不可编辑模式下或点数不足时，直接返回null，避免不必要的计算
  if (!props.editable || internalPoints.value.length < 2) return null

  // 获取曲线对象
  const line = lineRef.value?.getNode()
  if (!line) return null

  // 提取不带isHovered属性的点数组
  const points = internalPoints.value.map((p) => ({ x: p.x, y: p.y }))

  // 使用工具函数查找插入点
  const result = findCurveInsertionPoint(
    point,
    points,
    props.tension,
    actualDetectThreshold.value,
    internalClosed.value
  )

  return result
}

// 计算曲线配置
const curveConfig = computed(() => {
  const flatPoints = internalPoints.value.reduce((acc, point) => {
    acc.push(point.x, point.y)
    return acc
  }, [] as number[])

  return {
    points: flatPoints,
    stroke: props.curveColor,
    strokeWidth: actualCurveWidth.value,
    lineCap: 'round',
    lineJoin: 'round',
    tension: props.tension,
    closed: internalClosed.value,
    hitStrokeWidth: actualDetectThreshold.value,
    // 点击曲线，添加点
    onClick: (e: KonvaEventObject<MouseEvent>) => {
      e.cancelBubble = true

      if (!props.editable) return
      if (!props.allowAddPoints || (internalClosed.value && !props.allowAddPointsWhenClosed)) return
      // 通过事件对象获取stage
      const stage = e.target.getStage()
      if (!stage) return
      const pos = stage.getPointerPosition()
      if (!pos) return
      const insertionPoint = findInsertionPoint(pos)
      if (insertionPoint) {
        internalPoints.value.splice(insertionPoint.index, 0, {
          x: insertionPoint.point.x,
          y: insertionPoint.point.y,
          isHovered: false
        })
        syncPointsToParent()
        groupRef.value?.getNode()?.getLayer()?.batchDraw()
      }
    },
    // 鼠标悬停移动，两种情况都会触发，一是曲线本身，二是曲线闭合后的整个区域
    onMousemove: () => {
      if (
        props.editable &&
        (props.allowAddPoints || (internalClosed.value && props.allowAddPointsWhenClosed))
      ) {
        // document.body.style.cursor = 'crosshair'
        if (!props.editable) {
          emit('activate')
        }
      }
    },
    onMouseout: () => {
      document.body.style.cursor = 'default'
      // console.log('mouseout')
    },
    onmousedown: (e: KonvaEventObject<MouseEvent>) => {
      emit('activate')
      e.cancelBubble = true // 阻止事件冒泡到stage
    }
  }
})

// 处理图层点击事件，如果点在空白处，则添加点
const handleGroupClick = (e: KonvaEventObject<MouseEvent>) => {
  // console.log(e.target.className)
  if (!props.editable) return
  const stage = e.target.getStage()
  if (!stage) return
  const pos = stage.getPointerPosition()
  if (!pos) return
  // 只排除锚点和曲线，其他都允许加点
  const isShape = ['Circle', 'Line'].includes(e.target.className)
  if (isShape) return
  // 如果不是鼠标左键，则不处理
  if (e.evt.button !== 0) return
  // 检查是否点击在第一个点附近，如果是则闭合曲线
  if (internalPoints.value.length > 2 && internalPoints.value[0]) {
    const firstPoint = internalPoints.value[0]
    const distance = getDistance(pos, { x: firstPoint.x, y: firstPoint.y })
    if (distance < 20) {
      internalClosed.value = true
      return
    }
  }
  if (!props.allowAddPoints) return
  if (internalClosed.value) {
    return
  } else {
    internalPoints.value.push({
      x: pos.x,
      y: pos.y,
      isHovered: false
    })
    syncPointsToParent()
    groupRef.value?.getNode()?.getLayer()?.batchDraw()
  }
}

// 处理锚点拖动
const handleAnchorDragMove = (e: KonvaEventObject<DragEvent>, index: number) => {
  if (!props.editable || !props.allowDragPoints) return

  const pos = e.target.position()
  const anchor = internalPoints.value[index]

  // 更新锚点位置
  if (anchor) {
    anchor.x = pos.x
    anchor.y = pos.y

    // 同步到父组件
    syncPointsToParent()

    // 手动触发图层重绘
    groupRef.value?.getNode()?.getLayer()?.batchDraw()
  }
}

// 处理锚点鼠标悬停
const handleAnchorMouseOver = (index: number) => {
  const anchor = internalPoints.value[index]
  if (anchor) {
    anchor.isHovered = true

    // 如果是第一个点，并且有足够的点可以闭合曲线，显示特殊的指针
    if (index === 0 && internalPoints.value.length > 2 && !internalClosed.value) {
      document.body.style.cursor = 'cell' // 使用cell指针表示可以闭合
    } else {
      document.body.style.cursor = 'pointer'
    }
  }
}

// 处理锚点鼠标离开
const handleAnchorMouseOut = (index: number) => {
  const anchor = internalPoints.value[index]
  if (anchor) {
    anchor.isHovered = false
    document.body.style.cursor = 'default'
  }
}

// 处理锚点点击事件
const handleAnchorClick = (index: number, e: KonvaEventObject<MouseEvent>) => {
  if (!props.editable) return

  e.cancelBubble = true

  // 如果点击的是第一个点，并且有足够的点可以闭合曲线
  if (index === 0 && internalPoints.value.length > 2 && !internalClosed.value) {
    internalClosed.value = true
    // 手动触发图层重绘
    groupRef.value?.getNode()?.getLayer()?.batchDraw()
  }
}

// 清理函数
const cleanupResources = () => {
  // 重置状态
  document.body.style.cursor = 'default'
}

// 组件挂载后的初始化
onMounted(() => {
  initializePoints()
})

// 组件卸载前的清理
onUnmounted(() => {
  cleanupResources()
})
</script>

<style scoped lang="less">
.spline-curve-editor {
  position: relative;
  canvas {
    display: block;
  }
}
</style>
