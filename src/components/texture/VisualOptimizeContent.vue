<template>
  <div class="visual-optimize-page" :style="{ height: `calc(100vh - ${headerHeight}px)` }">
    <!-- 全屏画布区域 - 复用SmileCanvas组件 -->
    <SmileCanvas
      v-if="smileImgObj"
      ref="canvasRef"
      :showMidline="showMidline"
      :showSmileFrame="showSmileFrame"
      :showMouthImage="false"
      :showFrameControls="false"
      :interactiveTeeth="false"
      :isTexturePage="true"
    />

    <!-- 浮动控制面板 -->
    <div class="floating-controls">
      <!-- 微笑控制面板 - 纹理页面专用版本 -->
      <TexturePhotoControlPanel v-model:showSmileFrame="showSmileFrame" />

      <!-- 纹理选择面板 -->
      <TexturePanel
        :textures="textures"
        :modelValue="selectedTextureId || undefined"
        @update:modelValue="selectedTextureId = $event"
      />

      <!-- 色彩调整面板 -->
      <ColorAdjustPanel
        :hue="colorAdjustments.hue"
        :saturation="colorAdjustments.saturation"
        :brightness="colorAdjustments.brightness"
        :baseColor="colorAdjustments.baseColor"
        :textureStrength="colorAdjustments.textureStrength"
        @update:hue="setHue"
        @update:saturation="setSaturation"
        @update:brightness="setBrightness"
        @update:baseColor="setBaseColor"
        @update:textureStrength="setTextureStrength"
      />

      <!-- 阴影效果面板 -->
      <ShadowPanel
        :intensity="shadowSettings.intensity"
        @update:intensity="shadowSettings.intensity = $event"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { shallowRef, onMounted, onActivated, nextTick } from 'vue'
import { useTextureEditorContext } from '@/contexts/TextureEditorContext'
import { useTextureManager } from '@/composables/texture/useTextureManager'
import { useWindowResize } from '@/composables/useWindowResize'
import { useTextureStore } from '@/store/texture'
import { useTeethStore } from '@/store/teeth'
import SmileCanvas from '@/components/SmileCanvas.vue'
import TexturePhotoControlPanel from '@/components/texture/TexturePhotoControlPanel.vue'
import TexturePanel from '@/components/texture/TexturePanel.vue'
import ColorAdjustPanel from '@/components/texture/ColorAdjustPanel.vue'
import ShadowPanel from '@/components/texture/ShadowPanel.vue'

// 获取纹理编辑器上下文 - 使用专用的纹理编辑器上下文
const editorContext = useTextureEditorContext()

// 获取store
const teethStore = useTeethStore()

// 从上下文中获取需要的属性
const headerHeight = editorContext.headerHeight
const smileImgObj = editorContext.smileImgObj
const showMidline = editorContext.showMidline
const showSmileFrame = editorContext.showSmileFrame

// 获取纹理相关状态和方法
const {
  textures,
  selectedTextureId,
  colorAdjustments,
  shadowSettings,
  preloadTextures,
  setTexture,
  setHue,
  setSaturation,
  setBrightness,
  setBaseColor,
  setTextureStrength
} = useTextureManager()

// 画布引用
const canvasRef = shallowRef<InstanceType<typeof SmileCanvas> | null>(null)

// 窗口resize监听
useWindowResize(() => {
  if (canvasRef.value && canvasRef.value.canvasContainerRef) {
    editorContext.updateStageSize(canvasRef.value.canvasContainerRef)
  }
})

// 在组件激活时更新局部框架位置
onActivated(() => {
  // 强制更新牙齿位置
  nextTick(() => {
    // 强制更新牙齿形状，触发牙齿位置更新
    teethStore.forceUpdateTeethShapes()
    // console.log('纹理页面：组件激活，强制更新牙齿位置')

    // 更新局部框架位置
    editorContext.updateLocalFramePosition()
  })
})

// 组件挂载时预加载纹理
onMounted(async () => {
  try {
    await preloadTextures()
    // 获取纹理store的完整引用，以访问hasInitializedTexture
    const textureStore = useTextureStore()

    // 只有在首次进入纹理页面时，才设置默认选中第一个纹理
    if (!textureStore.hasInitializedTexture) {
      setTexture(textures.value[0]!.id)
    }

    // 确保纹理显示开关打开
    textureStore.setShowTexture(true)
  } catch (error) {
    // console.error('纹理预加载失败:', error)
  }
})
</script>

<style scoped>
.visual-optimize-page {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f7f8fa;
  overflow: hidden;
}

.floating-controls {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  width: 280px;
}
</style>
