<template>
  <v-group>
    <!-- 框架 - 使用纹理页面专用的框架组件，使用局部框架位置 -->
    <TextureTeethFrameSimple :stageScale="props.stageScale" :visible="props.showSmileFrame" />

    <!-- 牙齿纹理渲染 - 唇线裁剪，只裁剪牙齿纹理内容，不裁剪框架线 -->
    <v-group v-if="props.showTexture && segments.length > 0" :clipFunc="clipLipRegion">
      <!-- 牙齿纹理渲染 -->
      <TextureToothOutline
        v-for="segment in segments"
        :key="`texture-${segment.index}`"
        :segmentInfo="segment"
        :toothId="segment.index"
        :showTexture="props.showTexture"
      />
      <!-- 唇线区域内阴影，放在牙齿纹理之上，强度变化时强制刷新 -->
      <!-- <v-shape :sceneFunc="drawLipInnerShadow" :key="shadowSettings.intensity" /> -->
    </v-group>
  </v-group>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import 'inset.js'
import { useTeethStore } from '@/store/teeth'
import { useCommonStore } from '@/store/common'
import { imageToStage as imageToStageUtil } from '@/utils/transform2d'
import TextureTeethFrameSimple from './TextureTeethFrameSimple.vue'
import TextureToothOutline from './TextureToothOutline.vue'
import { useTextureEditorContext } from '@/contexts/TextureEditorContext'
import { calculateCurvePoint } from '@/utils/curveUtils'
import { useTextureManager } from '@/composables/texture/useTextureManager'

// Props
const props = defineProps({
  stageScale: {
    type: Number,
    default: 1
  },
  showTexture: {
    type: Boolean,
    default: true
  },
  showSmileFrame: {
    type: Boolean,
    default: true
  }
})

// Store
const teethStore = useTeethStore()
const segments = computed(() => teethStore.segments)

// 获取唇线点和编辑器上下文
const commonStore = useCommonStore()
const smileLipPoints = computed(() => commonStore.smileLipPoints)
const editorContext = useTextureEditorContext()

// 缓存图片坐标下的平滑点，提升clip性能
let lastImgPoints: { x: number; y: number }[] = []
let lastSmoothImgPoints: { x: number; y: number }[] = []

const { shadowSettings } = useTextureManager()

function getCachedSmoothImgPoints(
  imgPoints: { x: number; y: number }[],
  tension: number,
  segmentSamples: number
) {
  if (
    lastSmoothImgPoints.length &&
    imgPoints.length === lastImgPoints.length &&
    imgPoints.every(
      (p, i) => lastImgPoints[i] && p.x === lastImgPoints[i].x && p.y === lastImgPoints[i].y
    )
  ) {
    return lastSmoothImgPoints
  }
  // 重新计算
  const n = imgPoints.length
  const smoothPoints: { x: number; y: number }[] = []
  for (let i = 0; i < n; i++) {
    const p1 = imgPoints[i]
    const p2 = imgPoints[(i + 1) % n]
    const prev = imgPoints[i === 0 ? n - 1 : i - 1]
    const next = imgPoints[(i + 2) % n]
    if (!p1 || !p2 || !prev || !next) continue
    for (let j = 0; j < segmentSamples; j++) {
      const t = j / segmentSamples
      const pt = calculateCurvePoint(p1, p2, tension, t, prev, next)
      if (!pt) continue
      smoothPoints.push(pt)
    }
  }
  lastImgPoints = imgPoints.map((p) => ({ ...p }))
  lastSmoothImgPoints = smoothPoints
  return smoothPoints
}

// 裁剪函数，先缓存图片坐标下的平滑点，clip时批量变换
function clipLipRegion(ctx: CanvasRenderingContext2D) {
  // 1. 用图片坐标的唇线点生成平滑点
  const imgPoints = smileLipPoints.value
  const tension = 0.5
  const segmentSamples = 16
  if (!imgPoints || imgPoints.length < 2) return

  const smoothImgPoints = getCachedSmoothImgPoints(imgPoints, tension, segmentSamples)
  // 2. 批量变换到画布坐标
  const getTransformParams = editorContext.getTransformParams
  const smoothStagePoints = smoothImgPoints.map((pt) => imageToStageUtil(pt, getTransformParams()))

  if (!smoothStagePoints.length || !smoothStagePoints[0]) return
  ctx.beginPath()
  ctx.moveTo(smoothStagePoints[0].x, smoothStagePoints[0].y)
  for (let i = 1; i < smoothStagePoints.length; i++) {
    const pt = smoothStagePoints[i]
    if (!pt) continue
    ctx.lineTo(pt.x, pt.y)
  }
  ctx.closePath()
}

// 唇线区域内阴影绘制函数
function drawLipInnerShadow(ctx: CanvasRenderingContext2D) {
  // 1. 生成唇线区域平滑点（与clipLipRegion一致）
  const imgPoints = smileLipPoints.value
  const tension = 0.5
  const segmentSamples = 16
  if (!imgPoints || imgPoints.length < 2) return

  const smoothImgPoints = getCachedSmoothImgPoints(imgPoints, tension, segmentSamples)
  const getTransformParams = editorContext.getTransformParams
  const smoothStagePoints = smoothImgPoints.map((pt) => imageToStageUtil(pt, getTransformParams()))

  if (!smoothStagePoints.length || !smoothStagePoints[0]) return

  ctx.save()
  ctx.beginPath()
  ctx.moveTo(smoothStagePoints[0].x, smoothStagePoints[0].y)
  for (let i = 1; i < smoothStagePoints.length; i++) {
    const pt = smoothStagePoints[i]
    if (!pt) continue
    ctx.lineTo(pt.x, pt.y)
  }
  ctx.closePath()
  ctx.clip()

  // 获取底层原生 context
  const rawCtx =
    ctx.canvas && (ctx.canvas as any)._canvas ? (ctx.canvas as any)._canvas.getContext('2d') : ctx

  // 记录原始 shadowInset 状态
  const prevInset = rawCtx.shadowInset

  // 只在本次 fill 前后设置
  rawCtx.shadowInset = true
  rawCtx.shadowColor = `rgba(0,0,0,${shadowSettings.value.intensity})`
  rawCtx.shadowBlur = 50
  rawCtx.shadowOffsetX = 0
  rawCtx.shadowOffsetY = 0

  ctx.beginPath()
  ctx.moveTo(smoothStagePoints[0].x, smoothStagePoints[0].y)
  for (let i = 1; i < smoothStagePoints.length; i++) {
    const pt = smoothStagePoints[i]
    if (!pt) continue
    ctx.lineTo(pt.x, pt.y)
  }
  ctx.closePath()
  ctx.fillStyle = 'rgba(0,0,0,0)'
  ctx.fill()

  // 恢复原始 shadowInset 状态，避免影响其他 shape
  rawCtx.shadowInset = prevInset
  ctx.restore()
}
</script>
