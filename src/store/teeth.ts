import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 点的接口定义
export interface Point {
  x: number
  y: number
}

// 定义从中心向外各区段的宽度比例
export const SEGMENT_WIDTH_RATIOS_CENTER_OUT = [11, 8, 7, 5, 3]

// 分割区块信息接口
export interface SegmentInfo {
  index: number // 区块索引
  startX: number // 区块起始X坐标
  endX: number // 区块结束X坐标
  width: number // 区块宽度
  topY: number // 区块上边界Y坐标
  bottomY: number // 区块下边界Y坐标
  height: number // 区块高度
  centerX: number // 区块中心X坐标
  centerY: number // 区块中心Y坐标
  isLeft: boolean // 是否在中心线左侧
}

// 牙齿数据接口
export interface Tooth {
  id: number
  position: number // 牙齿在框架中的相对位置(0-1)
  centerX: number // 牙齿中心X坐标(相对于框架)
  centerY: number // 牙齿中心Y坐标(相对于框架)
  width: number // 牙齿宽度
  height: number // 牙齿高度
  rotation: number // 牙齿旋转角度
  points: Point[] // 牙齿轮廓点(相对于牙齿中心)
}

// 框架数据接口
export interface TeethFrame {
  x: number // 框架在画布中的X坐标
  y: number // 框架在画布中的Y坐标
  width: number // 框架宽度
  height: number // 框架高度

  // 新的曲线控制参数
  topCurvePoints: {
    leftY: number // 上曲线左端点Y坐标
    midY: number // 上曲线中点Y坐标
    rightY: number // 上曲线右端点Y坐标
  }
  bottomCurvePoints: {
    leftY: number // 下曲线左端点Y坐标
    midY: number // 下曲线中点Y坐标
    rightY: number // 下曲线右端点Y坐标
  }
}

// 牙齿形状数据接口
export interface ToothShape {
  // 相对于中心点的比例位置，范围通常在 -1 到 1 之间
  points: { x: number; y: number }[]
}

// 模板数据接口
export interface TeethTemplate {
  id: string
  name: string
  // 每个区块的牙齿形状，索引对应区块索引
  toothShapes: { [key: number]: ToothShape }
}

// 默认的框架数据
const defaultFrame: TeethFrame = {
  x: 0,
  y: 0,
  width: 500,
  height: 125, // 减小框架高度，从150减小到125
  // 新的曲线控制参数
  topCurvePoints: {
    leftY: 0,
    midY: 12.5, // 初始下沉为框架高度的10%
    rightY: 0
  },
  bottomCurvePoints: {
    leftY: 125, // 与框架高度一致
    midY: 150, // 初始下沉为框架高度的20%
    rightY: 125 // 与框架高度一致
  }
}

// 创建三角形牙齿形状
const createTriangleToothShapes = (): { [key: number]: ToothShape } => {
  // 不使用createDefaultToothShapes，而是从头创建形状
  const shapes: { [key: number]: ToothShape } = {}

  // 控制点数量
  const NUM_CONTROL_POINTS = 12

  // 区块索引范围通常是 -5 到 5，不包括 0
  for (let i = -5; i <= 5; i++) {
    if (i !== 0) {
      const points: Point[] = []

      // 创建三角形形状的控制点
      for (let j = 0; j < NUM_CONTROL_POINTS; j++) {
        const angle = (j / NUM_CONTROL_POINTS) * Math.PI * 2
        let x = Math.cos(angle) * 0.9 // 基本形状
        const y = Math.sin(angle) * 0.9

        // 根据点的位置调整形状
        if (j < 3 || j > 9) {
          // 上部和下部的点，使其更尖锐
          x = x * 0.8
        }

        // 对左侧牙齿（负索引）进行X方向镜像
        if (i < 0) {
          x = -x
        }

        points.push({
          x: x,
          y: j < 3 || j > 9 ? y * 1.1 : y // 上部和下部的点，使其更尖锐
        })
      }

      shapes[i] = { points }
    }
  }

  return shapes
}

// 创建椭圆形牙齿形状
const createOvalToothShapes = (): { [key: number]: ToothShape } => {
  // 不使用createDefaultToothShapes，而是从头创建形状
  const shapes: { [key: number]: ToothShape } = {}

  // 控制点数量
  const NUM_CONTROL_POINTS = 12

  // 区块索引范围通常是 -5 到 5，不包括 0
  for (let i = -5; i <= 5; i++) {
    if (i !== 0) {
      const points: Point[] = []

      // 创建椭圆形状的控制点
      for (let j = 0; j < NUM_CONTROL_POINTS; j++) {
        const angle = (j / NUM_CONTROL_POINTS) * Math.PI * 2
        let x = Math.cos(angle) * 0.9 // 基本形状
        let y = Math.sin(angle) * 0.9 * 1.2 // 垂直方向拉长

        // 对左侧牙齿（负索引）进行X方向镜像
        if (i < 0) {
          x = -x
        }

        points.push({ x, y })
      }

      shapes[i] = { points }
    }
  }

  return shapes
}

// 创建方形牙齿形状
const createSquareToothShapes = (): { [key: number]: ToothShape } => {
  // 不使用createDefaultToothShapes，而是从头创建形状
  const shapes: { [key: number]: ToothShape } = {}

  // 控制点数量
  const NUM_CONTROL_POINTS = 12

  // 区块索引范围通常是 -5 到 5，不包括 0
  for (let i = -5; i <= 5; i++) {
    if (i !== 0) {
      const points: Point[] = []

      // 创建方形形状的控制点
      for (let j = 0; j < NUM_CONTROL_POINTS; j++) {
        const angle = (j / NUM_CONTROL_POINTS) * Math.PI * 2
        let x = Math.cos(angle) * 0.9 // 基本形状
        let y = Math.sin(angle) * 0.9

        // 水平方向拉宽，垂直方向压缩
        x = x > 0 ? Math.min(0.9, x * 1.1) : Math.max(-0.9, x * 1.1)
        y = y > 0 ? Math.min(0.9, y * 0.9) : Math.max(-0.9, y * 0.9)

        // 对左侧牙齿（负索引）进行X方向镜像
        if (i < 0) {
          x = -x
        }

        points.push({ x, y })
      }

      shapes[i] = { points }
    }
  }

  return shapes
}

import triangleData from '../data/triangle.json'
import ovalData from '../data/oval.json'
import squareData from '../data/square.json'
import triangleMData from '../data/triangleM.json'
import ovalMData from '../data/ovalM.json'
import squareMData from '../data/squareM.json'

// 创建默认模板
const createDefaultTemplates = (): TeethTemplate[] => {
  // 创建各种形状的牙齿
  const triangleShapes = triangleData
  const ovalShapes = ovalData
  const squareShapes = squareData
  const triangleMShapes = triangleMData
  const ovalMShapes = ovalMData
  const squareMShapes = squareMData

  return [
    {
      id: 'Triangle',
      name: 'Triangle',
      toothShapes: triangleShapes
    },
    {
      id: 'Oval',
      name: 'Oval',
      toothShapes: ovalShapes
    },
    {
      id: 'Square',
      name: 'Square',
      toothShapes: squareShapes
    },
    // 其他模板可以在这里添加
    {
      id: 'TriangleM',
      name: 'TriangleM',
      toothShapes: triangleMShapes
    },
    {
      id: 'OvalM',
      name: 'OvalM',
      toothShapes: ovalMShapes
    },
    {
      id: 'SquareM',
      name: 'SquareM',
      toothShapes: squareMShapes
    }
  ]
}

export const useTeethStore = defineStore('teeth', () => {
  // 状态
  const templates = ref<TeethTemplate[]>(createDefaultTemplates())
  const currentTemplateId = ref<string>('Triangle')
  const frame = ref<TeethFrame>({ ...defaultFrame })
  const selectedToothId = ref<number | null>(null)

  const isTeethVisible = ref<boolean>(true) // 牙齿可见性可能仍然是全局的
  const segments = ref<SegmentInfo[]>([])

  // 存储自定义的牙齿形状
  const customToothShapes = ref<{ [key: number]: ToothShape }>({})

  // 计算属性
  const currentTemplate = computed(() =>
    templates.value.find((t) => t.id === currentTemplateId.value)
  )

  // 获取当前牙齿形状
  const getCurrentToothShape = (toothId: number) => {
    // 首先检查是否有自定义形状
    if (customToothShapes.value[toothId]) {
      return customToothShapes.value[toothId]
    }

    // 然后检查当前模板是否有该牙齿的形状
    const template = currentTemplate.value
    if (template && template.toothShapes && template.toothShapes[toothId]) {
      return template.toothShapes[toothId]
    }

    // 如果都没有，返回null
    return null
  }

  // 方法
  // 设置当前模板
  function setCurrentTemplate(templateId: string) {
    const template = templates.value.find((t) => t.id === templateId)
    if (template) {
      // 检查是否是相同的模板ID
      const isSameTemplate = currentTemplateId.value === templateId

      // 1. 只有在切换到不同的模板时才重置自定义牙齿形状
      // 避免在导航到同一模板的页面时重置用户修改
      if (!isSameTemplate) {
        resetAllToothShapes() // 这会清空自定义形状
      }
      // 2. 更新模板ID
      currentTemplateId.value = templateId

      // 3. 重置选中状态 (无论是否切换模板，都取消选中)
      selectedToothId.value = null

      // 4. 强制更新segments以触发牙齿重新渲染
      // 即使是相同模板，如果用户手动重置了，也需要重新渲染
      if (segments.value.length > 0) {
        // 创建一个新数组，保持原有的引用不变
        const newSegments = [...segments.value]
        segments.value = newSegments
      }

      // 5. 如果是相同的模板ID，并且发生了重置（比如用户点击了重置按钮），额外触发一次更新
      // 实际上，如果 !isSameTemplate，resetAllToothShapes() 已经触发了 segments 更新
      // 如果 isSameTemplate，但需要强制更新，这部分逻辑可能需要更明确的触发点
      // 暂时保留，但需要注意其作用
      if (isSameTemplate) {
        // 延迟执行，确保前面的更新已经完成
        setTimeout(() => {
          // 再次强制更新segments
          if (segments.value.length > 0) {
            const newSegments = [...segments.value]
            segments.value = newSegments
          }
        }, 0)
      }
    } else {
      console.error(`未找到ID为 ${templateId} 的模板`)
    }
  }

  // 更新框架
  function updateFrame(newFrame: Partial<TeethFrame>) {
    // 处理特殊情况：如果只更新了topCurvePoints或bottomCurvePoints的部分属性
    if (newFrame.topCurvePoints && !newFrame.topCurvePoints.leftY && frame.value.topCurvePoints) {
      newFrame.topCurvePoints = {
        ...frame.value.topCurvePoints,
        ...newFrame.topCurvePoints
      }
    }

    if (
      newFrame.bottomCurvePoints &&
      !newFrame.bottomCurvePoints.leftY &&
      frame.value.bottomCurvePoints
    ) {
      newFrame.bottomCurvePoints = {
        ...frame.value.bottomCurvePoints,
        ...newFrame.bottomCurvePoints
      }
    }

    frame.value = { ...frame.value, ...newFrame }
    // 更新牙齿位置
  }

  // 选择牙齿
  function selectTooth(id: number | null) {
    selectedToothId.value = id
  }

  // 更新自定义牙齿形状
  function updateToothShape(toothId: number, shape: ToothShape) {
    customToothShapes.value = {
      ...customToothShapes.value,
      [toothId]: shape
    }
    console.log('customToothShapes', customToothShapes.value)
  }

  // 重置所有自定义牙齿形状
  function resetAllToothShapes() {
    // 1. 清空自定义形状
    // 创建一个新对象以确保触发响应式更新
    customToothShapes.value = {}

    // 2. 重置选中状态
    selectedToothId.value = null

    // 3. 强制更新segments以触发牙齿重新渲染
    if (segments.value.length > 0) {
      // 创建一个新数组，保持原有的引用不变
      const newSegments = [...segments.value]
      segments.value = newSegments
    }

    // 4. 重置框架位置（如果需要）
    // 注意：这里不重置框架位置，因为框架位置由initFrameBasedOnLipPosition处理
  }

  /**
   * 强制更新牙齿形状
   * 不改变当前模板，但强制重新应用模板形状到所有牙齿
   */
  function forceUpdateTeethShapes() {
    // 强制更新segments以触发牙齿重新渲染
    if (segments.value.length > 0) {
      // 创建一个新数组，保持原有的引用不变
      const newSegments = [...segments.value]
      segments.value = newSegments
    }

    // 这里我们使用一个特殊的技巧：临时修改currentTemplateId然后改回来
    // 这样可以触发依赖于 currentTemplateId 的组件重新渲染
    const originalId = currentTemplateId.value
    // 使用一个不存在的ID，确保不会匹配到任何模板
    currentTemplateId.value = 'FORCE_UPDATE_' + Date.now()
    // 立即改回原来的ID
    setTimeout(() => {
      currentTemplateId.value = originalId
    }, 0)
  }

  return {
    templates,
    currentTemplateId,
    frame,
    selectedToothId,
    isTeethVisible,
    segments,
    customToothShapes,
    currentTemplate,
    setCurrentTemplate,
    updateFrame,
    selectTooth,
    getCurrentToothShape,
    updateToothShape,
    resetAllToothShapes,
    forceUpdateTeethShapes
  }
})
