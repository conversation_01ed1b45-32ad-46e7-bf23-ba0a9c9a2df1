import { defineStore } from 'pinia'

import smilePic from '@/assets/images/smilePic2.jpg'
import openmouthPic from '@/assets/images/openmouthPic.jpg'

export interface CommonState {
  headerHeight: number
  /** 微笑照图片（主照片） */
  smileImage: string | null
  /** 开口照图片（口内照片） */
  mouthImage: string | null

  /** 微笑照的面部关键点（x/y 均为图片坐标系下的像素点） */
  smileFaceLandmarks: import('@/types/face').FacePoint[]
  /** 微笑照的旋转角度 */
  faceRotation: number
  /** 标记用户是否手动调整过面部关键点 */
  hasManuallyAdjustedFacePoints: boolean
  /** 微笑照的唇线点集 */
  smileLipPoints: { x: number; y: number }[]
  /** 开口照的唇线点集 */
  mouthLipPoints: { x: number; y: number }[]
  /** 微笑照唇线点的原始备份（用于重置） */
  originalSmileLipPoints: { x: number; y: number }[] | null

  /** 微笑照的对齐点（AI给出，用于自动/手动对齐，通常为2个点） */
  smileAlignPoints: { x: number; y: number }[]
  /** 开口照的对齐点（AI给出，用于自动/手动对齐，通常为2个点） */
  mouthAlignPoints: { x: number; y: number }[]
  /** 手动对齐时的初始点（用于重设） */
  initialSmileAlignPoints: { x: number; y: number }[]
  initialMouthAlignPoints: { x: number; y: number }[]

  /** 开口照的仿射变换参数（对齐产出） */
  mouthToSmileTransform: {
    angle: number // 旋转角度（弧度）
    scale: number // 缩放比例
    offsetX: number // X方向平移
    offsetY: number // Y方向平移
  }
}

export const useCommonStore = defineStore('common', {
  state: (): CommonState => ({
    headerHeight: 104, // 头部高度

    smileImage: smilePic, // 微笑照图片
    mouthImage: openmouthPic, // 开口照图片

    smileFaceLandmarks: [], // 微笑照面部关键点(AI给初始值，步骤2修改产出)
    faceRotation: 0, // 微笑照旋转角度(步骤2产出)
    hasManuallyAdjustedFacePoints: false, // 标记用户是否手动调整过面部关键点

    smileLipPoints: [], // 微笑照唇线点（步骤3产出）
    mouthLipPoints: [], // 开口照唇线点（AI获得）
    originalSmileLipPoints: null, // 微笑照唇线点原始备份（用于重置，AI产出）

    smileAlignPoints: [], // 微笑照对齐点
    mouthAlignPoints: [], // 开口照对齐点
    initialSmileAlignPoints: [],
    initialMouthAlignPoints: [],

    /** 开口照的仿射变换参数（相对微笑照）（对齐产出） */
    mouthToSmileTransform: {
      angle: 0, // 开口照旋转角度(步骤3产出)
      scale: 1, // 开口照缩放比例(步骤3产出)
      offsetX: 0, // 开口照X方向平移(步骤3产出)
      offsetY: 0 // 开口照Y方向平移(步骤3产出)
    }
  }),

  actions: {
    /**
     * 重置用户手动调整关键点的标志
     * 调用此方法后，下次导航到 face-landmark 页面时会重新获取 AI 检测数据
     */
    resetFacePointsAdjustmentFlag() {
      this.hasManuallyAdjustedFacePoints = false
    }
  }
})
