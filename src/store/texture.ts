import { defineStore } from 'pinia'

/**
 * 纹理状态接口
 */
export interface TextureState {
  // 当前选中的纹理ID
  selectedTextureId: string | null
  // 纹理不透明度
  textureOpacity: number
  // 是否显示纹理
  showTexture: boolean
  // 色彩调整参数
  colorAdjustments: {
    hue: number // 色相调整 (-180 到 180)
    saturation: number // 饱和度调整 (-1 到 1)
    brightness: number // 亮度调整 (-1 到 1)
    baseColor: string // 牙齿基础颜色
    textureStrength: number // 纹理强度 (0 到 1)
  }
  // 阴影设置
  shadowSettings: {
    // enabled: boolean // 是否启用阴影
    intensity: number // 阴影强度 (0 到 1)
    // range: number // 阴影范围 (像素)
  }
  // 是否已经初始化过纹理选择
  hasInitializedTexture: boolean
}

/**
 * 默认纹理状态
 */
const defaultState: TextureState = {
  selectedTextureId: null,
  textureOpacity: 0.85, // 设置为固定值，因为控制面板中已移除纹理强度滑块
  showTexture: true, // 默认显示纹理
  colorAdjustments: {
    hue: 0,
    saturation: 0,
    brightness: 0,
    baseColor: '#FFFAF0', // 默认象牙白色
    textureStrength: 0.7 // 默认纹理强度
  },
  shadowSettings: {
    // enabled: true,
    intensity: 1
    // range: 5
  },
  hasInitializedTexture: false // 初始状态为未初始化
}

/**
 * 纹理状态管理Store
 */
export const useTextureStore = defineStore('texture', {
  state: (): TextureState => ({
    selectedTextureId: defaultState.selectedTextureId,
    textureOpacity: defaultState.textureOpacity,
    showTexture: defaultState.showTexture,
    colorAdjustments: { ...defaultState.colorAdjustments },
    shadowSettings: { ...defaultState.shadowSettings },
    hasInitializedTexture: defaultState.hasInitializedTexture
  }),

  actions: {
    /**
     * 设置当前纹理
     * @param id 纹理ID
     */
    setTexture(id: string | null) {
      this.selectedTextureId = id
      // 标记已经初始化过纹理选择
      this.hasInitializedTexture = true
    },

    /**
     * 标记纹理已初始化
     */
    markTextureInitialized() {
      this.hasInitializedTexture = true
    },

    /**
     * 设置纹理不透明度
     * @param opacity 不透明度值 (0-1)
     */
    setTextureOpacity(opacity: number) {
      this.textureOpacity = opacity
    },

    /**
     * 设置是否显示纹理
     * @param show 是否显示
     */
    setShowTexture(show: boolean) {
      this.showTexture = show
    },

    /**
     * 设置色相调整
     * @param value 色相值 (-180 到 180)
     */
    setHue(value: number) {
      this.colorAdjustments.hue = value
    },

    /**
     * 设置饱和度调整
     * @param value 饱和度值 (-1 到 1)
     */
    setSaturation(value: number) {
      this.colorAdjustments.saturation = value
    },

    /**
     * 设置亮度调整
     * @param value 亮度值 (-1 到 1)
     */
    setBrightness(value: number) {
      this.colorAdjustments.brightness = value
    },

    /**
     * 设置牙齿基础颜色
     * @param color 色值 (如 '#FFFAF0')
     */
    setBaseColor(color: string) {
      this.colorAdjustments.baseColor = color
    },

    /**
     * 设置纹理强度
     * @param value 强度值 (0 到 1)
     */
    setTextureStrength(value: number) {
      this.colorAdjustments.textureStrength = value
    },

    /**
     * 设置阴影启用状态
     * @param enabled 是否启用
     */
    // setShadowEnabled(enabled: boolean) {
    //   this.shadowSettings.enabled = enabled
    // },

    /**
     * 设置阴影强度
     * @param value 强度值 (0 到 1)
     */
    setShadowIntensity(value: number) {
      this.shadowSettings.intensity = value
    },

    /**
     * 设置阴影范围
     * @param value 范围值 (像素)
     */
    // setShadowRange(value: number) {
    //   this.shadowSettings.range = value
    // },

    /**
     * 重置所有纹理设置到默认值
     */
    resetTexture() {
      this.selectedTextureId = defaultState.selectedTextureId
      this.textureOpacity = defaultState.textureOpacity
      this.showTexture = defaultState.showTexture
      this.colorAdjustments = { ...defaultState.colorAdjustments }
      this.shadowSettings = { ...defaultState.shadowSettings }
      // 不重置hasInitializedTexture，保留用户已经初始化过的状态
    }
  }
})
