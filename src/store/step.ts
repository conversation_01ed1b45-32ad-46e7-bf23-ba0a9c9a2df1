import { defineStore } from 'pinia'

export const useStepStore = defineStore('step', {
  state: () => ({
    activeIndex: 0, // 当前激活步骤索引
    maxActiveIndex: 0 // 历史最大激活索引
  }),
  actions: {
    setActiveIndex(idx: number) {
      this.activeIndex = idx
      if (idx > this.maxActiveIndex) {
        this.maxActiveIndex = idx
      }
    },
    reset() {
      this.activeIndex = 0
      this.maxActiveIndex = 0
    }
  }
}) 