import request from '@/utils/request'
import { API_PATHS } from './constants'
import type { 
  LoginParams, 
  LoginResponse, 
  User, 
  UserResponse, 
  VoidResponse
} from '@/types/auth'

/**
 * 用户认证相关 API
 */
export const authApi = {
  /**
   * 用户登录
   * @param params 登录参数
   */
  login(params: LoginParams): Promise<LoginResponse> {
    return request.post(API_PATHS.AUTH.LOGIN, params)
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser(): Promise<UserResponse> {
    return request.get(API_PATHS.USER.GET_INFO)
  },

  /**
   * 退出登录
   */
  logout(): Promise<VoidResponse> {
    return request.post(API_PATHS.AUTH.LOGOUT)
  },

  /**
   * 更新用户信息
   * @param data 要更新的用户信息
   */
  updateUserInfo(data: Partial<User>): Promise<UserResponse> {
    return request.put(API_PATHS.USER.UPDATE, data)
  }
} 