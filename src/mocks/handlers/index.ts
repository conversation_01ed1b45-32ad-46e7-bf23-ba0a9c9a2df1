import { http, HttpResponse, passthrough } from 'msw'
import type {
  LoginParams,
  LoginResponse,
  User,
  UserResponse,
  VoidResponse,
  LoginResult
} from '@/types/auth'
import { API_PATHS, API_BASE_URL } from '@/api/constants'
import { mockUsers, mockToken } from '../data'
import type { FaceLandmark } from '@/types/face'

// 确保 mock 用户一定存在
const defaultUser = mockUsers[0]
if (!defaultUser) {
  throw new Error('Mock user data is missing')
}

const mockSmileLipPoints = [
  {
    x: 1159.44362489591,
    y: 2972.896559543347
  },
  {
    x: 1102.5810526002679,
    y: 2915.4911608719967
  },
  {
    x: 1231.3266185293905,
    y: 2884.393348942852
  },
  {
    x: 1336.9897257988864,
    y: 2843.7590020644398
  },
  {
    x: 1540.1600089601866,
    y: 2815.7532545504237
  },
  {
    x: 1727.4281534264558,
    y: 2761.5160189376775
  },
  {
    x: 1855.7765452053368,
    y: 2718.348320156872
  },
  {
    x: 1980.0719761092623,
    y: 2675.308402925712
  },
  {
    x: 1936.1440713711365,
    y: 2743.1170170926207
  },
  {
    x: 1876.8147317237433,
    y: 2807.002101835874
  },
  {
    x: 1823.3189993831038,
    y: 2854.549675334087
  },
  {
    x: 1742.7304388576092,
    y: 2910.521289582237
  },
  {
    x: 1647.2713026997926,
    y: 2949.8169792328545
  },
  {
    x: 1549.5696421165353,
    y: 2981.506808324425
  },
  {
    x: 1424.4172841910813,
    y: 3000.003686742137
  },
  {
    x: 1295.8568096135643,
    y: 2986.6875772501853
  }
]
const mockMouthLipPoints = [
  {
    x: 1787.4368440124128,
    y: 4264.9614928789415
  },
  {
    x: 1909.572458196367,
    y: 4250.64296726743
  },
  {
    x: 1980.1232394942786,
    y: 4239.361881076411
  },
  {
    x: 2070.9547952896028,
    y: 4240.444021985189
  },
  {
    x: 2221.7328860736425,
    y: 4257.010682771535
  },
  {
    x: 2344.110907853981,
    y: 4261.244957330581
  },
  {
    x: 2477.8530436570168,
    y: 4256.734701091235
  },
  {
    x: 2594.9491214656177,
    y: 4245.834185670633
  },
  {
    x: 2721.838762827325,
    y: 4235.0023383634825
  },
  {
    x: 2604.9536968582265,
    y: 4311.901636854438
  },
  {
    x: 2487.9411289953623,
    y: 4362.597969960899
  },
  {
    x: 2352.1182000044573,
    y: 4414.263309030766
  },
  {
    x: 2169.069763041195,
    y: 4422.126304950431
  },
  {
    x: 2030.1032649962035,
    y: 4380.475321038326
  },
  {
    x: 1918.5537250657117,
    y: 4327.865393770613
  }
]

const mockSmileAlignPoints = [
  { x: 1457, y: 2909 },
  { x: 1696, y: 2833 }
]
const mockMouthAlignPoints = [
  {
    x: 2104,
    y: 4500
  },
  {
    x: 2426,
    y: 4482
  }
]

const mockLandmark: FaceLandmark = {
  points: [
    { name: 'leftEye', x: 754, y: 1978 },
    { name: 'rightEye', x: 1697, y: 1676 },
    { name: 'leftNostril', x: 1073, y: 2421 },
    { name: 'rightNostril', x: 1751, y: 2236 },
    { name: 'leftMouth', x: 1041, y: 2929 },
    { name: 'rightMouth', x: 2000, y: 2672 }
  ],
  smileLipPoints: mockSmileLipPoints,
  mouthLipPoints: mockMouthLipPoints,
  smileAlignPoints: mockSmileAlignPoints,
  mouthAlignPoints: mockMouthAlignPoints,
  lipPoints: mockSmileLipPoints // 兼容老逻辑
}

export const handlers = [
  // Passthrough requests for Vue component styles handled by Vite
  http.get(/.*\.vue(\?.*&type=style.*)?$/, () => {
    return passthrough()
  }),
  http.get(/.*\.bmp$/, () => {
    return passthrough()
  }),

  // mock /api/face/landmark
  http.get(API_BASE_URL + API_PATHS.FACE.LANDMARK, () => {
    return HttpResponse.json({ code: 0, data: mockLandmark })
  }),

  // 登录接口
  http.post(API_BASE_URL + API_PATHS.AUTH.LOGIN, async ({ request }) => {
    const body = (await request.json()) as LoginParams

    if (body.username === 'admin' && body.password === '123456') {
      const loginResult: LoginResult = {
        token: mockToken,
        user: defaultUser
      }

      return HttpResponse.json<LoginResponse>({
        code: 200,
        data: loginResult,
        message: '登录成功'
      })
    }

    return HttpResponse.json(
      {
        code: 401,
        message: '用户名或密码错误',
        data: null
      } as LoginResponse,
      { status: 401 }
    )
  }),

  // 获取用户信息
  http.get(API_BASE_URL + API_PATHS.USER.GET_INFO, () => {
    return HttpResponse.json<UserResponse>({
      code: 200,
      data: defaultUser,
      message: '获取成功'
    })
  }),

  // 登出接口
  http.post(API_BASE_URL + API_PATHS.AUTH.LOGOUT, () => {
    return HttpResponse.json<VoidResponse>({
      code: 200,
      data: undefined,
      message: '登出成功'
    })
  }),

  // 更新用户信息
  http.put(API_BASE_URL + API_PATHS.USER.UPDATE, async ({ request }) => {
    const body = (await request.json()) as Partial<User>
    const updatedUser: User = {
      ...defaultUser,
      ...(body as User) // 强制类型转换,因为我们知道合并后会是完整的 User
    }

    return HttpResponse.json<UserResponse>({
      code: 200,
      data: updatedUser,
      message: '更新成功'
    })
  })
]
