// 全局通用类型定义

// API 响应相关类型
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
}

// 分页结果
export interface PaginationResult<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// 通用类型
export type ID = string | number
export type Status = 'active' | 'inactive' | 'pending' | 'deleted'

// 时间戳字段
export interface Timestamps {
  createdAt: string
  updatedAt: string
}

// 树节点
export interface TreeNode<T> {
  id: ID
  parentId: ID | null
  children?: TreeNode<T>[]
  data: T
}

// 选项类型
export interface Option {
  label: string
  value: any
  disabled?: boolean
  children?: Option[]
}

// 文件信息
export interface FileInfo {
  name: string
  size: number
  type: string
  url: string
}

// 字典类型
export type Dictionary<T> = Record<string, T>

// 主题
export type Theme = 'light' | 'dark' | 'system'

// 用户偏好设置
export interface UserPreferences {
  theme: Theme
  language: string
  notifications: boolean
}

// 加载状态
export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

// 响应式尺寸
export type ResponsiveSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl'

// 方向
export type Direction = 'horizontal' | 'vertical'

// 对齐方式
export type Alignment = 'left' | 'center' | 'right' | 'justify'

// 导出所有类型
export {
  ApiResponse,
  PaginationParams,
  PaginationResult,
  ID,
  Status,
  Timestamps,
  TreeNode,
  Option,
  FileInfo,
  Dictionary,
  Theme,
  UserPreferences,
  LoadingState,
  ResponsiveSize,
  Direction,
  Alignment
} 