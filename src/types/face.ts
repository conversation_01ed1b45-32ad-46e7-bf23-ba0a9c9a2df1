// 面部关键点类型定义
export type FacePointName =
  | 'leftEye'
  | 'rightEye'
  | 'leftNostril'
  | 'rightNostril'
  | 'leftMouth'
  | 'rightMouth'

export interface FacePoint {
  name: FacePointName
  x: number
  y: number
}

export interface FaceLandmark {
  points: FacePoint[]
  smileLipPoints?: { x: number; y: number }[]
  mouthLipPoints?: { x: number; y: number }[]
  lipPoints?: { x: number; y: number }[]
  smileAlignPoints?: { x: number; y: number }[]
  mouthAlignPoints?: { x: number; y: number }[]
}
