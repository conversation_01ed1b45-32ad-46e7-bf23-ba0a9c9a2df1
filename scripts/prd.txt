# DSD微笑设计系统 - 产品需求文档 (PRD)

> 重要说明：本项目专注于前端实现，后端和AI模块将由其他团队提供API接口。本文档中后端和AI相关描述仅作为前端开发的参考信息。

## 1. 产品概述

### 1.1 产品背景

DSD（Digital Smile Design）是一款专业的牙科数字化微笑设计软件，旨在帮助医生为患者设计自然美观的微笑。通过AI技术，将传统1小时以上的设计时间缩短至10分钟，大幅提升诊所效率。

### 1.2 产品价值

- 提升医患沟通效率：患者可直观预览整牙效果
- 降低设计门槛：AI辅助使小白医生也能快速上手
- 提高诊所效率：将设计时间从1小时缩短至10分钟

## 2. 产品功能需求

### 2.1 图片导入

- 支持拖拽导入微笑照片和开口器牵引照片
- 支持常见图片格式（JPG、PNG等）
- 自动进行图片质量检测和优化

### 2.2 角度调整

- AI自动检测面部关键点（瞳孔、鼻翼、口角）
- 自动确定面部中线
- 提供手动微调关键点的功能
- 实时预览调整效果

### 2.3 唇线编辑

- AI自动检测唇部位置和范围
- 自动生成唇部范围曲线
- 支持手动微调微笑曲线
- 实时预览效果

### 2.4 照片对齐

- 加载"微笑照"+"口内照"，自动分析并配准对齐
- 可手动细微调整两张图片上的3个辅助点，进行叠加对齐
- 支持透明度调节以便对比
- 实时预览对齐效果

### 2.5 贴面应用

- 提供6种基础贴面模板供选择：
  * 男性：三角形、椭圆形、正方形
  * 女性：三角形、椭圆形、正方形
- AI自动推荐最适合患者面型的贴面模板

#### 贴面调整功能

- 镜像调整：对称调整左右两侧牙齿上的贴面位置
- 连锁调整：同步调整相邻牙齿贴面的边界位置
- 精细调整：对单颗牙齿上的贴面进行多点位置调整
- 镜像复制：将一侧牙齿上的贴面位置复制到对侧
- 整体调整：统一调整一组贴面在牙齿上的位置和轮廓

### 2.6 视觉优化

#### 2.6.1 纹理效果

- 提供多种预设纹理效果
- 支持纹理强度调节
- 实时预览效果

#### 2.6.2 色彩调节

- 支持调节：
  * 色相
  * 饱和度
  * 亮度
- 提供预设色彩方案
- 实时预览效果

#### 2.6.3 阴影效果

- 自动添加唇部阴影
- 支持阴影强度和范围调节
- 实时预览效果

### 2.7 精细收尾修整

#### 2.7.1 液化工具

- 用于修整原始牙齿多余部分
- 支持笔刷大小调节
- 支持撤销/重做操作

#### 2.7.2 印章工具

- 支持任意位置取色
- 提供笔刷大小调节
- 支持透明度调节
- 支持撤销/重做操作



### 2.8 结果分享

- 生成二维码在线查看
- 支持短信分享
- 支持邮件分享
- 支持本地保存

## 3. 非功能需求

### 3.1 性能需求

- 软件启动时间：≤3秒
- AI分析响应时间：≤5秒
- 图片处理响应时间：≤2秒
- 实时编辑响应时间：≤0.1秒

### 3.2 兼容性需求

- 支持Windows 10及以上版本
- 支持macOS 10.15及以上版本
- 支持主流浏览器最新版本（Chrome、Firefox、Safari、Edge）

### 3.3 安全性需求

- 患者数据本地存储加密
- 网络传输SSL加密
- 自动隐私保护（眼睛遮挡）
- 用户授权管理

### 3.4 可用性需求

- 界面设计符合医疗软件规范
- 提供新手引导教程
- 支持快捷键操作
- 支持中英文界面切换

## 4. 技术方案

### 4.1 前端技术栈（本项目范围）

- 框架：Vue 3 + TypeScript + Vite
- 状态管理：Pinia
- 路由：Vue Router
- 2D图形：Konva.js
- 国际化：Vue I18n
- 样式：Less
- UI组件库：Element Plus
- HTTP请求：Axios
- 单元测试：Vitest
- E2E测试：Cypress

### 4.2 后端技术栈（参考信息）

- 框架：Spring Boot
- ORM：MyBatis
- 数据库：MySQL
- 部署：Docker + Nginx

### 4.3 AI模块（参考信息）

- 训练框架：PyTorch/TensorFlow
- 部署方式：服务端API调用
- 主要算法：
  * 人脸关键点检测
  * 唇线自动检测
  * 照片自动对齐
  * 贴面智能匹配

## 5. 项目规划

### 5.1 前端开发周期

- 第一阶段（2周）：项目基础搭建
  * 技术栈整合
  * 基础组件开发
  * 路由设计
  * 状态管理方案
  
- 第二阶段（2周）：核心功能开发
  * 图片管理模块
  * 基础编辑功能
  * 界面布局实现
  
- 第三阶段（2周）：高级功能开发
  * 贴面应用工具
  * 视觉优化功能
  * 测量标注功能
  
- 第四阶段（2周）：优化和测试
  * 性能优化
  * 单元测试
  * E2E测试
  * 浏览器兼容性测试

### 5.2 迭代计划

- v1.0：基础功能版本
- v1.1：AI功能优化版本
- v1.2：性能优化版本
- v2.0：完整商业版本

## 6. 风险评估

### 6.1 技术风险

- AI模型准确性不足
- 实时渲染性能问题
- 数据安全性问题

### 6.2 解决方案

- 持续优化AI模型
- 实施严格的数据加密方案


### 一些补充
国际化可以放到最后再做，优先级比较低。
