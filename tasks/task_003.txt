# Task ID: 3
# Title: 面部关键点检测与自动旋转校正
# Status: done
# Dependencies: 2, 11
# Priority: high
# Description: 实现面部关键点检测与自动旋转校正功能。用户可拖动6个关键点（leftEye, rightEye, leftNostril, rightNostril, leftMouth, rightMouth），系统根据两组关键点连线（瞳孔、口角）的平均倾斜角度，自动旋转照片，使面部“摆正”。同时，自动计算并绘制面部垂直中线（基于三组关键点中点的X坐标平均值）。支持重置关键点到AI初始状态。所有数据本地响应式管理，后端接口暂用mock模拟。
# Details:
1. 类型定义与工具函数
2. mock API与数据结构
3. 主编辑器组件开发
4. 中线与旋转逻辑实现
5. 重置与状态管理
6. UI与交互优化
7. 测试与验证

# Test Strategy:
测试AI检测的准确性，验证手动微调功能是否灵敏且准确，检查实时预览是否流畅，确保面部中线计算正确
