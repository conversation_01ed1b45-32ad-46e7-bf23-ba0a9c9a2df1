# Task ID: 5
# Title: 照片对齐与配准功能
# Status: done
# Dependencies: 4, 11
# Priority: medium
# Description: 实现微笑照与口内照的自动对齐和手动微调功能
# Details:
1. 从store获取两张图片（整脸）和AI检测的点、唇线点集。
2. 通过唇线点集计算牙齿区域包围盒，所有图片和点渲染都只显示该区域，参考LipEdit.vue的聚焦算法。
3. 初始状态下，左侧为两张图片的半透明层叠（开口照已自动对齐），右侧为两张各带AI点的图片。
4. 点击'手动对齐照片'后，用户可在每张图片上各自点选两个新点，点选完成后左侧重新渲染层叠效果。
5. 对齐算法：微笑照为基准，开口照根据两点的水平距离比值(scale)和连线角度差(rotate)变换。
6. 支持'重设对齐状态'按钮，恢复到AI自动对齐初始状态。
7. 交互细节与UI提示需完善，点选时有引导，支持撤销/重选，结果可保存。
8. 图片与点的坐标变换、牙齿区域聚焦算法参考LipEdit.vue的calcLipFocusScaleAndOffset、getLipBoundingBox等实现。
9. 所有图片和点的渲染、对齐、交互均需基于牙齿区域的缩放/偏移参数进行。

# Test Strategy:
1. 测试自动对齐的准确性，验证AI点对齐后两张图片的层叠效果是否自然。
2. 验证手动点选后对齐算法的正确性，确保开口照能准确scale和rotate到微笑照基准。
3. 检查牙齿区域聚焦显示是否准确，图片和点位均应只显示牙齿区域。
4. 测试'重设对齐状态'按钮能否恢复到AI自动对齐初始状态。
5. 验证点选交互的引导、撤销/重选、保存等功能是否完善。
6. 检查所有坐标变换、缩放、偏移逻辑在不同分辨率下的鲁棒性。

# Subtasks:
## 1. 设置牙齿区域聚焦与图像初始化 [done]
### Dependencies: None
### Description: 实现牙齿区域的计算、聚焦和初始图像加载功能
### Details:
实现步骤：
1. 从store获取微笑照与口内照两张图片及AI检测的唇线点集
2. 基于唇线点集实现计算牙齿区域包围盒的函数，参考LipEdit.vue中的getLipBoundingBox
3. 实现牙齿区域缩放和偏移计算函数，参考calcLipFocusScaleAndOffset
4. 创建组件的基本结构，包括图像容器和初始状态的数据模型
5. 实现图像加载和初始化逻辑，确保两张图片都正确渲染

测试方法：
- 验证图像是否正确加载并显示
- 检查牙齿区域是否正确计算和聚焦
- 确认初始状态下基本组件结构是否正确

## 2. 实现AI自动对齐功能 [done]
### Dependencies: 5.1
### Description: 基于AI点集实现两张照片的自动对齐和叠加显示
### Details:
实现步骤：
1. 基于AI检测的点集计算开口照相对于微笑照的初始变换参数(scale和rotate)
2. 实现变换算法，使开口照根据计算出的参数进行缩放和旋转
3. 创建左侧视图，实现两张照片的半透明叠加效果
4. 创建右侧视图，分别显示带有AI点的两张照片
5. 确保所有渲染都基于之前计算的牙齿区域聚焦参数

测试方法：
- 验证自动对齐后的图像叠加效果
- 检查右侧分开显示的图像是否正确显示AI点
- 确认所有图像都聚焦在牙齿区域

## 3. 实现手动对齐交互功能 [done]
### Dependencies: 5.2
### Description: 开发用户手动选择对齐点的交互界面和功能
### Details:
实现步骤：
1. 添加'手动对齐照片'按钮及其触发逻辑
2. 实现点选模式的状态管理，包括当前正在点选的图片和已选点的追踪
3. 为每张图片添加点击事件处理，支持用户在图片上选择参考点
4. 添加视觉引导和提示，明确告知用户当前应在哪张图片上点选
5. 实现点选操作的撤销和重选功能
6. 添加点选完成的判断逻辑，在用户完成所有点(每张图片两个点)后自动进入下一步

测试方法：
- 测试点选交互是否流畅直观
- 验证撤销和重选功能是否正常工作
- 检查视觉引导是否清晰有效

## 4. 实现手动对齐算法与重新渲染 [done]
### Dependencies: 5.3
### Description: 基于用户选择的点实现对齐算法并更新图像显示
### Details:
实现步骤：
1. 实现基于用户选择点的对齐算法，计算scale(水平距离比值)和rotate(连线角度差)
2. 使用计算出的参数对开口照进行变换(以微笑照为基准)
3. 更新左侧视图的叠加显示，反映手动对齐后的效果
4. 添加'重设对齐状态'按钮，实现恢复到AI自动对齐的初始状态
5. 确保所有变换和渲染都基于牙齿区域的聚焦参数

测试方法：
- 验证手动选点后的对齐效果
- 测试重设功能是否正确恢复到初始状态
- 检查边界情况，如用户选择的点过于接近时的处理

## 5. 完善UI交互与结果保存 [done]
### Dependencies: 5.4
### Description: 优化用户界面，添加交互反馈和结果保存功能
### Details:
实现步骤：
1. 添加半透明度调节控制，允许用户调整叠加图像的透明度
2. 完善点选过程中的视觉反馈，包括已选点的显示和连线
3. 添加操作提示和帮助信息，提高用户体验
4. 实现对齐结果的保存功能，将变换参数存入store或通过事件传递给父组件
5. 添加加载和处理状态的指示器
6. 进行最终的UI polish和边缘情况处理

测试方法：
- 测试透明度调节是否有效
- 验证所有视觉反馈和提示是否清晰
- 确认结果保存功能是否正确工作
- 进行端到端测试，验证整体功能流程

