# Task ID: 2
# Title: 图片导入与管理模块
# Status: done
# Dependencies: 1, 11
# Priority: high
# Description: 实现简洁的图片导入功能，包括微笑照片和开口器照片的上传与预览
# Details:
1. 创建两个简单的图片上传区域，分别用于微笑照片和开口器照片
2. 实现点击或拖拽上传功能
3. 上传后在对应区域显示图片预览
4. 添加简单的文件类型验证（仅接受图片格式）
5. 提供重新上传的选项
6. 确保界面设计符合提供的设计图，保持简洁

# Test Strategy:
测试不同格式和大小的图片上传，验证拖拽和点击上传功能是否正常，确保图片预览正确显示，检查文件类型验证是否正确工作

# Subtasks:
## 1. 图片上传组件与拖拽功能实现 [done]
### Dependencies: None
### Description: 创建图片上传组件，实现拖拽上传功能和基础文件类型验证
### Details:
实现步骤：
1. 创建可复用的图片上传组件(ImageUploader)
2. 实现拖拽区域(DropZone)，支持文件拖放和点击上传
3. 添加文件类型验证逻辑，限制只接受JPG、PNG等图片格式
4. 实现上传状态反馈（进度条或加载动画）
5. 添加错误处理和用户提示

测试方法：
- 测试拖拽不同类型文件的处理
- 验证文件类型限制是否正确执行
- 确认上传状态反馈是否正常工作

<info added on 2025-05-02T07:12:29.870Z>
根据设计图的简化要求，建议添加以下内容：

实现细节：
1. 使用React的useRef和useState钩子管理上传状态和文件引用
2. 图片上传组件应包含以下核心功能：
   - 隐藏的input[type="file"]元素，通过ref绑定
   - 可点击区域触发文件选择对话框
   - 拖拽区域使用onDragOver、onDragEnter、onDragLeave和onDrop事件处理
3. 文件类型验证可使用简单的MIME类型检查：
   ```javascript
   const isValidFileType = (file) => {
     const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
     return validTypes.includes(file.type);
   }
   ```
4. 用户反馈可通过简单的状态消息实现，如"上传成功"、"不支持的文件类型"等

组件接口设计：
```javascript
<ImageUploader 
  onFileSelect={(file) => {/* 处理选中的文件 */}}
  maxFileSizeMB={5}
  acceptedFileTypes={['image/jpeg', 'image/png']}
/>
```

注意事项：
- 确保拖拽区域有明确的视觉提示，如虚线边框和提示文本
- 实现时考虑移动设备兼容性，在不支持拖拽的设备上优雅降级为仅点击上传
</info added on 2025-05-02T07:12:29.870Z>

## 2. 双区域图片上传界面实现 [done]
### Dependencies: 2.1
### Description: 根据设计图创建微笑照片和开口器照片的两个上传区域
### Details:
实现步骤：
1. 创建页面布局，包含中央提示文本和两个并排的上传区域
2. 左侧区域设置为微笑照片上传，添加相应提示文本
3. 右侧区域设置为开口器照片上传，添加相应提示文本
4. 确保布局在不同屏幕尺寸下保持良好的响应式设计
5. 添加"清晰的微笑照片有助于产生更好的结果"的说明文本

测试方法：
- 验证页面布局是否符合设计图要求
- 测试不同屏幕尺寸下的响应式表现
- 确认文本提示是否清晰可见

<info added on 2025-05-02T07:12:57.015Z>
更新后的实现步骤：

1. 创建页面布局，包含中央提示文本"导入或选择照片"
2. 添加说明文本"清晰的微笑照片有助于产生更好的结果"
3. 左侧区域设置为微笑照片上传，添加提示文本"点击或拖拽微笑照片到此处"
4. 右侧区域设置为开口器照片上传，添加提示文本"点击或拖拽开口照片到此处"
5. 确保布局在不同屏幕尺寸下保持良好的响应式设计

技术实现细节：
1. 使用CSS Grid或Flexbox创建两列布局，确保在移动设备上可以自动切换为单列布局
2. 为上传区域实现拖放功能，使用HTML5的drag and drop API
3. 每个上传区域应包含：
   - 图标指示（如相机或上传图标）
   - 提示文本
   - 点击触发文件选择器的交互区域
4. 添加视觉反馈，当用户拖动文件到区域上时改变区域样式
5. 在小屏幕设备上（<768px）将两个上传区域垂直堆叠而非水平排列
6. 使用媒体查询确保文本在各种设备上保持可读性
</info added on 2025-05-02T07:12:57.015Z>

## 3. 图片预览与重新上传功能 [done]
### Dependencies: 2.1, 2.2
### Description: 实现上传后的图片预览和重新上传选项
### Details:
实现步骤：
1. 在每个上传区域内实现图片上传后的预览功能
2. 添加重新上传按钮，允许用户替换已上传的图片
3. 确保预览图片适当缩放以适应上传区域
4. 添加简单的上传成功/失败提示

测试方法：
- 测试上传不同尺寸图片后的预览效果
- 验证重新上传功能是否正确替换原图片
- 测试上传成功/失败提示是否正常显示

<info added on 2025-05-02T07:13:37.143Z>
根据设计图，我们需要简化此子任务的实现，专注于图片预览功能。以下是具体实现细节：

图片预览实现方案：
1. 使用FileReader API读取上传文件并生成预览
```javascript
const reader = new FileReader();
reader.onload = (e) => {
  previewElement.src = e.target.result;
  previewContainer.classList.remove('hidden');
  uploadContainer.classList.add('hidden');
};
reader.readAsDataURL(file);
```

2. 图片缩放处理：
   - 使用CSS的object-fit: cover属性确保图片填充预览区域
   - 设置预览容器固定尺寸，例如150px × 150px

3. 重新上传UI交互：
   - 在预览图片上方添加半透明遮罩层，显示"重新上传"按钮
   - 点击时触发input[type="file"]的click事件

4. 上传状态提示：
   - 使用简单的toast消息或内联提示
   - 上传中状态显示loading spinner
   - 成功/失败状态使用不同颜色和图标区分

注意事项：
- 确保在移动设备上预览区域响应式调整
- 图片上传失败时保留原预览，并显示错误提示
- 考虑添加图片格式和大小验证
</info added on 2025-05-02T07:13:37.143Z>

## 4. 与步骤导航栏的集成 [done]
### Dependencies: 2.3
### Description: 确保图片上传模块与任务11中实现的步骤导航栏正确集成
### Details:
实现步骤：
1. 将图片上传页面与步骤导航栏正确集成
2. 确保当两张图片都成功上传后，用户可以进入下一步
3. 添加适当的验证，防止用户在未上传所需图片前进入下一步
4. 实现返回上一步的功能，确保已上传的图片状态被保留

测试方法：
- 验证导航栏在图片上传页面中的正确显示
- 测试未上传图片时进入下一步的限制功能
- 测试返回上一步后图片状态的保留情况

<info added on 2025-05-02T07:14:00.364Z>
实现步骤补充：
1. 在图片上传组件中引入步骤导航栏组件，确保其在页面顶部正确显示
2. 使用Pinia创建专门的图片存储模块，例如：
```javascript
// stores/imageStore.js
import { defineStore } from 'pinia'

export const useImageStore = defineStore('images', {
  state: () => ({
    frontImage: null,
    backImage: null,
    uploadStatus: {
      front: false,
      back: false
    }
  }),
  getters: {
    canProceed: (state) => state.uploadStatus.front && state.uploadStatus.back,
    getImages: (state) => ({
      front: state.frontImage,
      back: state.backImage
    })
  },
  actions: {
    setImage(type, image) {
      if (type === 'front') {
        this.frontImage = image;
        this.uploadStatus.front = true;
      } else if (type === 'back') {
        this.backImage = image;
        this.uploadStatus.back = true;
      }
    },
    clearImages() {
      this.frontImage = null;
      this.backImage = null;
      this.uploadStatus.front = false;
      this.uploadStatus.back = false;
    }
  }
})
```

3. 在步骤导航栏中添加状态检查，根据Pinia存储的上传状态动态启用/禁用"下一步"按钮：
```vue
<template>
  <button 
    :disabled="!imageStore.canProceed" 
    @click="goToNextStep"
    :class="{'button-disabled': !imageStore.canProceed, 'button-enabled': imageStore.canProceed}"
  >
    下一步
  </button>
</template>
```

4. 实现图片预览功能，当用户上传图片后立即显示预览，并更新Pinia状态

5. 添加错误处理机制，当图片上传失败时提供清晰的用户反馈并允许重试
</info added on 2025-05-02T07:14:00.364Z>

