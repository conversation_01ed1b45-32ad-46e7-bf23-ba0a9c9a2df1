# Task ID: 1
# Title: 项目基础架构搭建
# Status: done
# Dependencies: None
# Priority: high
# Description: 搭建Vue 3 + TypeScript + Vite项目基础架构，集成必要的依赖和工具
# Details:
1. 使用Vite创建Vue 3 + TypeScript项目
2. 集成Pinia进行状态管理
3. 配置Vue Router实现路由管理
4. 安装并配置Element Plus UI组件库
5. 设置Less预处理器
6. 配置Axios用于HTTP请求
7. 设置项目目录结构（views, components, store, router, utils, assets等）
8. 配置ESLint和Prettier保证代码质量
9. 设置Vitest进行单元测试环境

# Test Strategy:
验证项目能够正常启动，各项依赖正确安装，基础路由可以正常跳转，确保开发环境运行稳定

# Subtasks:
## 1. 初始化Vue 3 + TypeScript + Vite项目并配置基础目录结构 [done]
### Dependencies: None
### Description: 使用Vite创建Vue 3 + TypeScript项目，并设置合理的项目目录结构
### Details:
1. 使用Vite CLI创建新项目: `npm create vite@latest my-project --template vue-ts`
2. 进入项目目录: `cd my-project`
3. 安装依赖: `npm install`
4. 创建以下目录结构:
   - src/views (页面组件)
   - src/components (可复用组件)
   - src/store (状态管理)
   - src/router (路由配置)
   - src/utils (工具函数)
   - src/assets (静态资源)
   - src/api (API请求)
   - src/types (TypeScript类型定义)
5. 创建基础文件:
   - src/App.vue (根组件)
   - src/main.ts (入口文件)
   - src/vite-env.d.ts (Vite类型声明)
6. 测试项目能否正常启动: `npm run dev`

## 2. 集成Vue Router和Pinia状态管理 [done]
### Dependencies: 1.1
### Description: 安装并配置Vue Router实现路由管理，集成Pinia进行状态管理
### Details:
1. 安装Vue Router: `yarn add vue-router@4`
2. 安装Pinia: `yarn add pinia`
3. 创建路由配置文件 src/router/index.ts:
   ```typescript
   import { createRouter, createWebHistory } from 'vue-router'
   import Home from '../views/Home.vue'

   const routes = [
     { path: '/', component: Home },
     { path: '/about', component: () => import('../views/About.vue') }
   ]

   const router = createRouter({
     history: createWebHistory(),
     routes
   })

   export default router
   ```
4. 创建Pinia store文件 src/store/index.ts:
   ```typescript
   import { createPinia } from 'pinia'

   const pinia = createPinia()

   export default pinia
   ```
5. 创建示例store src/store/counter.ts:
   ```typescript
   import { defineStore } from 'pinia'

   export const useCounterStore = defineStore('counter', {
     state: () => ({ count: 0 }),
     actions: {
       increment() { this.count++ }
     }
   })
   ```
6. 在main.ts中注册Router和Pinia:
   ```typescript
   import { createApp } from 'vue'
   import App from './App.vue'
   import router from './router'
   import pinia from './store'

   const app = createApp(App)
   app.use(router)
   app.use(pinia)
   app.mount('#app')
   ```
7. 创建简单的Home.vue和About.vue页面组件用于测试路由
8. 测试路由导航和状态管理是否正常工作

## 3. 集成Element Plus UI组件库和Less预处理器 [done]
### Dependencies: 1.1
### Description: 安装并配置Element Plus UI组件库，设置Less预处理器支持
### Details:
1. 安装Element Plus: `yarn add element-plus`
2. 安装Element Plus图标: `yarn add @element-plus/icons-vue`
3. 安装Less和相关依赖: `yarn add -D less less-loader`
4. 在main.ts中引入Element Plus:
   ```typescript
   import ElementPlus from 'element-plus'
   import 'element-plus/dist/index.css'
   // 已有的导入...

   app.use(ElementPlus)
   // 其他app配置...
   ```
5. 配置Vite支持Less，修改vite.config.ts:
   ```typescript
   import { defineConfig } from 'vite'
   import vue from '@vitejs/plugin-vue'

   export default defineConfig({
     plugins: [vue()],
     css: {
       preprocessorOptions: {
         less: {
           javascriptEnabled: true,
           additionalData: '@import "./src/assets/styles/variables.less";'
         }
       }
     }
   })
   ```
6. 创建全局样式文件:
   - src/assets/styles/variables.less (变量定义)
   - src/assets/styles/global.less (全局样式)
7. 创建一个测试组件，使用Element Plus组件和Less样式
8. 测试Element Plus组件是否正常显示，Less样式是否正确应用

## 4. 配置Axios和HTTP请求封装 [done]
### Dependencies: 1.1
### Description: 安装并配置Axios用于HTTP请求，创建请求拦截器和API封装
### Details:
1. 安装Axios: `yarn add axios`
2. 创建HTTP请求封装文件 src/utils/request.ts:
   ```typescript
   import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'

   const service = axios.create({
     baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
     timeout: 10000
   })

   // 请求拦截器
   service.interceptors.request.use(
     (config) => {
       // 可以在这里添加token等认证信息
       return config
     },
     (error) => Promise.reject(error)
   )

   // 响应拦截器
   service.interceptors.response.use(
     (response) => {
       const res = response.data
       // 可以根据后端返回的状态码进行统一处理
       return res
     },
     (error) => {
       console.error('请求错误:', error)
       return Promise.reject(error)
     }
   )

   export default service
   ```
3. 创建API模块示例 src/api/user.ts:
   ```typescript
   import request from '@/utils/request'

   export interface LoginParams {
     username: string
     password: string
   }

   export function login(data: LoginParams) {
     return request({
       url: '/user/login',
       method: 'post',
       data
     })
   }

   export function getUserInfo() {
     return request({
       url: '/user/info',
       method: 'get'
     })
   }
   ```
4. 创建环境变量文件 .env.development 和 .env.production 配置API基础URL
5. 创建一个简单的API调用示例组件
6. 使用Mock数据或实际API测试HTTP请求是否正常工作

## 5. 配置ESLint、Prettier和Vitest测试环境 [done]
### Dependencies: 1.1, 1.2, 1.3, 1.4
### Description: 设置ESLint和Prettier保证代码质量，配置Vitest进行单元测试
### Details:
1. 安装ESLint和相关插件:
   ```bash
   yarn add -D eslint eslint-plugin-vue @typescript-eslint/parser @typescript-eslint/eslint-plugin
   ```
2. 安装Prettier和相关插件:
   ```bash
   yarn add -D prettier eslint-config-prettier eslint-plugin-prettier
   ```
3. 安装Vitest和相关依赖:
   ```bash
   yarn add -D vitest @vue/test-utils jsdom
   ```
4. 创建ESLint配置文件 .eslintrc.js:
   ```javascript
   module.exports = {
     root: true,
     env: {
       browser: true,
       node: true,
       es2021: true
     },
     parser: 'vue-eslint-parser',
     parserOptions: {
       parser: '@typescript-eslint/parser',
       ecmaVersion: 2021,
       sourceType: 'module'
     },
     extends: [
       'plugin:vue/vue3-recommended',
       'eslint:recommended',
       '@typescript-eslint/recommended',
       'prettier'
     ],
     plugins: ['vue', '@typescript-eslint', 'prettier'],
     rules: {
       'prettier/prettier': 'error',
       'vue/multi-word-component-names': 'off'
     }
   }
   ```
5. 创建Prettier配置文件 .prettierrc:
   ```json
   {
     "semi": false,
     "singleQuote": true,
     "printWidth": 100,
     "trailingComma": "none",
     "tabWidth": 2
   }
   ```
6. 配置Vitest，修改vite.config.ts:
   ```typescript
   import { defineConfig } from 'vite'
   import vue from '@vitejs/plugin-vue'

   export default defineConfig({
     plugins: [vue()],
     test: {
       globals: true,
       environment: 'jsdom'
     }
     // 其他配置...
   })
   ```
7. 创建测试目录和示例测试:
   - 创建 src/components/HelloWorld.vue 组件
   - 创建 tests/components/HelloWorld.test.ts 测试文件
8. 在package.json中添加脚本:
   ```json
   "scripts": {
     "dev": "vite",
     "build": "vue-tsc && vite build",
     "lint": "eslint . --ext .vue,.js,.ts --fix",
     "format": "prettier --write .",
     "test": "vitest run",
     "test:watch": "vitest"
   }
   ```
9. 运行测试命令验证配置是否正确: `yarn test`
10. 运行lint命令检查代码质量: `yarn lint`

