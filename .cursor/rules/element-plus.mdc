---
description: 当使用element-plus组件的时候
globs: 
alwaysApply: false
---
# Element Plus 使用规范

## 组件引入

- **全局引入**
  ```typescript
  // src/main.ts
  import ElementPlus from 'element-plus'
  import 'element-plus/dist/index.css'
  import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
  
  app.use(ElementPlus, {
    locale: zhCn,
  })
  ```

- **按需引入**
  ```typescript
  // ✅ 推荐的按需引入方式
  import { ElButton, ElInput } from 'element-plus'
  
  // ❌ 避免直接引入整个库
  import ElementPlus from 'element-plus'
  ```

## 图标使用

- **图标引入**
  ```typescript
  // ✅ 推荐的图标引入方式
  import { Search, Edit, Delete } from '@element-plus/icons-vue'
  
  // 在模板中使用
  <el-icon><Search /></el-icon>
  ```

## 主题定制

- **变量覆盖**
  ```less
  // src/styles/element/index.less
  @import 'element-plus/theme-chalk/src/index.less';
  
  :root {
    --el-color-primary: #1890ff;
    --el-color-success: #52c41a;
    --el-color-warning: #faad14;
    --el-color-danger: #ff4d4f;
    
    // 字体大小
    --el-font-size-base: 14px;
    --el-font-size-large: 16px;
    --el-font-size-small: 13px;
    
    // 边框圆角
    --el-border-radius-base: 4px;
    --el-border-radius-small: 2px;
    
    // 间距
    --el-spacing-base: 8px;
    --el-spacing-large: 16px;
  }
  ```

## 最佳实践

- **组件使用**
  - 优先使用 Element Plus 提供的组件
  - 保持组件 API 的一致性
  - 合理使用插槽进行自定义

- **性能优化**
  - 大数据表格使用虚拟滚动
  - 合理使用懒加载组件
  - 避免过度嵌套弹窗组件

- **交互规范**
  - 统一使用 Element Plus 的确认框
  - 保持表单验证风格一致
  - 统一使用 Element Plus 的消息提示


