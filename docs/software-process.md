# 微笑设计功能流程

## 1. 图片导入

- 微笑照
- 开口器牵引照（口内照，作用是显示原来的牙齿，方便在其基础上上进行贴面设计）

## 2. 角度调整

- AI自动检测微笑照的关键点（瞳孔、鼻翼、口角），确定中线，完成图片位置的初调整
- 可手动微调关键点，进行细微调整

## 3. 唇线编辑

- 自动检测微笑照的唇部位置和范围，生成范围曲线
- 可手动微调微笑曲线

## 4. 照片对齐

- 加载"微笑照"+"口内照"，自动分析并配准对齐
- 可手动细微调整两张图片上的3个辅助点，进行叠加对齐

## 5. 贴面设计

### 主要功能

- 主要功能：针对口内照，应用并编辑贴面

### 贴面库

- 不同轮廓形状的牙齿（男女各三组：三角、椭圆、正方）

### 微笑线框

- 选择一组贴面后，可对这组贴面进行整体位置和轮廓的调整

### 贴面微调功能

- 镜像微调（左右两颗牙齿对称调整）
- 连锁微调（相邻两颗牙齿的边界同步移动）
- 精细微调（针对一颗牙齿的多个不规则点进行移动调整）
- 镜像拷贝（可以直接复制一侧的牙齿到另一侧）

### 其他

- 编辑好贴面后，切换前后图片的透明显示权重（微笑照在前，口内照在后），以唇线范围为蒙版，展示设计的贴面应用到微笑照上面的效果

## 6. 视觉调整

- 内置几种不同的纹理效果
- 色彩调节
  - 色相
  - 饱和度
  - 亮度
- 唇部阴影（第三步的唇线向内侧增加半透明黑色阴影）

## 7. 精细收尾修整

- 液化工具（用于删除底片（即微笑照）中原来的牙齿多余的露出部分，以便只显示新设计的贴面）
- 印章工具（用于改变颜色，可从画布中任意位置选取颜色，然后通过拖动画笔的方式应用到目标）

## 8. 结果查看与保存

- 滑动分割竖线展示前后对比
- 保存结果
- 图片隐私-遮挡眼睛部位

## 9. 距离测量（可选功能，暂不实现）

- 使用游标卡尺进行一颗牙齿的距离测量，然后在设计的贴片中进行标注，其他贴片根据像素计算进行距离显示

## 10. 结果分享

- 短信
- 邮件
- 二维码
