# DSD微笑设计系统

## 一、什么是DSD？

DSD（Digital Smile Design）​，中文叫数字微笑美学设计，是牙科领域的一项数字化技术，帮助医生为患者设计自然美观的微笑。

* 核心作用：
  1. 医患沟通：让患者提前看到"整牙后的效果"，减少沟通障碍。
  2. 生产指导：为瓷贴面、牙冠等修复体提供精准的设计参数（比如牙齿形状、颜色）。

* 传统方式的问题：
  * 过去医生用Photoshop手动设计，耗时长（1小时以上），依赖经验。
  * 患者难以理解专业术语，容易对效果产生误解。

## 二、我们的DSD软件要解决什么？

目标：让设计更快、更准、更直观！​

* 小白医生也能用：通过AI自动化，10分钟生成设计方案。
* 患者一眼看懂：动态对比图，直观展示"整牙前后"变化。

## 三、软件的核心功能

1. 一键导入照片：

* 患者的面部照片、口内照直接拖进软件，自动对齐（不用手动调整角度）。

2. AI智能设计：

* 自动分析：识别面部关键点（比如瞳孔、嘴唇），推荐适合的牙齿模板。
* 自动修图：AI帮你调整牙齿形状、颜色，生成"整牙效果图"。

3. 手动微调工具：

* 简单操作：拖动滑块改颜色、点几下调整牙齿大小，支持类似"左右对称编辑"这样的便捷操作。
* 3D预览：旋转查看牙齿的立体效果，确保和患者脸型匹配。

4. 快速导出：

* 生成二维码扫描的网页报告给患者。

## 四、亮点

1. AI帮助省时间：

* 传统设计需要医生手动画线，我们的AI能自动完成80%的基础工作。
* 比如：自动框选牙齿区域、匹配最佳牙齿模板。

2. 网页+软件双版本：

* 网页版：随时随地用浏览器打开，快速出图（适合咨询场景）。
* 软件版：安装到诊所电脑（Windows、Apple Mac电脑）。

## 五、软件功能和流程

参考 @software-process.md

## 六、技术方案

### 前端框架

采用 Vue 3 + TypeScript + Vite，具体技术栈：

* Vue：使用Composition API和setup语法糖
* Vue Router：路由管理
* Pinia：状态管理
* konvajs：2D图形编辑
* Vue I18n：国际化
* Less：样式预处理

### 2D图形渲染编辑模块

采用 konvajs 提供基础的2D图形编辑功能，包括：

* 图片编辑和变换
* 路径、曲线绘制和编辑
* 图层管理
* 滤镜效果

### AI辅助设计模块

* 部署方案：服务端API调用
* 主要功能：
  * 人脸关键点检测
  * 唇线自动检测
  * 照片自动对齐
  * 贴面智能匹配

### 对接方案

* 前后端分离架构
* RESTful API接口

### 云端技术

采用 Spring Boot + SpringMVC + MyBatis + MySQL开发，部署采用Docker + Nginx（反向代理）。

### AI辅助设计模块（2D算法框架）

* 训练框架：PyTorch/TensorFlow

对接方案，是在服务器端运行AI推理模型，提供http接口服务，客户端App通过网络接口调用来进行AI推理结果获取。
